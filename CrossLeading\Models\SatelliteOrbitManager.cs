using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using System.Xml;
using System.Xml.Serialization;

namespace CrossLeading.Models
{
    /// <summary>
    /// 卫星轨道根数管理器
    /// </summary>
    public static class SatelliteOrbitManager
    {
        private static readonly string XmlFilePath = Path.Combine(Application.StartupPath, "SatOrb.xml");
        private static readonly object lockObject = new object();

        /// <summary>
        /// 卫星轨道根数集合的XML根元素
        /// </summary>
        [Serializable]
        [XmlRoot("SatelliteOrbits")]
        public class SatelliteOrbitCollection
        {
            [XmlElement("Satellite")]
            public List<SatelliteOrbitElement> Satellites { get; set; }

            public SatelliteOrbitCollection()
            {
                Satellites = new List<SatelliteOrbitElement>();
            }
        }

        /// <summary>
        /// 加载所有卫星轨道根数
        /// </summary>
        /// <returns>卫星轨道根数列表</returns>
        public static List<SatelliteOrbitElement> LoadAllSatellites()
        {
            lock (lockObject)
            {
                try
                {
                    if (!File.Exists(XmlFilePath))
                    {
                        // 如果文件不存在，创建一个空的XML文件
                        CreateEmptyXmlFile();
                        return new List<SatelliteOrbitElement>();
                    }

                    XmlSerializer serializer = new XmlSerializer(typeof(SatelliteOrbitCollection));
                    using (FileStream fs = new FileStream(XmlFilePath, FileMode.Open, FileAccess.Read))
                    {
                        SatelliteOrbitCollection collection = (SatelliteOrbitCollection)serializer.Deserialize(fs);
                        return collection.Satellites ?? new List<SatelliteOrbitElement>();
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"加载卫星轨道数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return new List<SatelliteOrbitElement>();
                }
            }
        }

        /// <summary>
        /// 保存所有卫星轨道根数
        /// </summary>
        /// <param name="satellites">卫星轨道根数列表</param>
        /// <returns>保存是否成功</returns>
        public static bool SaveAllSatellites(List<SatelliteOrbitElement> satellites)
        {
            lock (lockObject)
            {
                try
                {
                    SatelliteOrbitCollection collection = new SatelliteOrbitCollection
                    {
                        Satellites = satellites ?? new List<SatelliteOrbitElement>()
                    };

                    XmlSerializer serializer = new XmlSerializer(typeof(SatelliteOrbitCollection));
                    
                    // 创建XML写入设置，格式化输出
                    XmlWriterSettings settings = new XmlWriterSettings
                    {
                        Indent = true,
                        IndentChars = "  ",
                        NewLineChars = "\r\n",
                        NewLineHandling = NewLineHandling.Replace
                    };

                    using (XmlWriter writer = XmlWriter.Create(XmlFilePath, settings))
                    {
                        serializer.Serialize(writer, collection);
                    }

                    return true;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"保存卫星轨道数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }
            }
        }

        /// <summary>
        /// 添加新的卫星轨道根数
        /// </summary>
        /// <param name="satellite">卫星轨道根数</param>
        /// <returns>添加是否成功</returns>
        public static bool AddSatellite(SatelliteOrbitElement satellite)
        {
            if (satellite == null)
                return false;

            var (isValid, errorMessage) = satellite.Validate();
            if (!isValid)
            {
                MessageBox.Show($"卫星数据验证失败: {errorMessage}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }

            List<SatelliteOrbitElement> satellites = LoadAllSatellites();
            
            // 检查是否已存在同名卫星
            if (satellites.Any(s => s.SatelliteName.Equals(satellite.SatelliteName, StringComparison.OrdinalIgnoreCase)))
            {
                MessageBox.Show($"卫星 '{satellite.SatelliteName}' 已存在！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }

            satellites.Add(satellite);
            return SaveAllSatellites(satellites);
        }

        /// <summary>
        /// 更新卫星轨道根数
        /// </summary>
        /// <param name="oldName">原卫星名称</param>
        /// <param name="updatedSatellite">更新后的卫星轨道根数</param>
        /// <returns>更新是否成功</returns>
        public static bool UpdateSatellite(string oldName, SatelliteOrbitElement updatedSatellite)
        {
            if (string.IsNullOrWhiteSpace(oldName) || updatedSatellite == null)
                return false;

            var (isValid, errorMessage) = updatedSatellite.Validate();
            if (!isValid)
            {
                MessageBox.Show($"卫星数据验证失败: {errorMessage}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }

            List<SatelliteOrbitElement> satellites = LoadAllSatellites();
            
            // 查找要更新的卫星
            var existingSatellite = satellites.FirstOrDefault(s => s.SatelliteName.Equals(oldName, StringComparison.OrdinalIgnoreCase));
            if (existingSatellite == null)
            {
                MessageBox.Show($"未找到卫星 '{oldName}'！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }

            // 如果名称发生变化，检查新名称是否已存在
            if (!oldName.Equals(updatedSatellite.SatelliteName, StringComparison.OrdinalIgnoreCase))
            {
                if (satellites.Any(s => s.SatelliteName.Equals(updatedSatellite.SatelliteName, StringComparison.OrdinalIgnoreCase)))
                {
                    MessageBox.Show($"卫星 '{updatedSatellite.SatelliteName}' 已存在！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }
            }

            // 更新卫星数据
            int index = satellites.IndexOf(existingSatellite);
            satellites[index] = updatedSatellite;

            return SaveAllSatellites(satellites);
        }

        /// <summary>
        /// 删除卫星轨道根数
        /// </summary>
        /// <param name="satelliteName">卫星名称</param>
        /// <returns>删除是否成功</returns>
        public static bool DeleteSatellite(string satelliteName)
        {
            if (string.IsNullOrWhiteSpace(satelliteName))
                return false;

            List<SatelliteOrbitElement> satellites = LoadAllSatellites();
            
            var satelliteToRemove = satellites.FirstOrDefault(s => s.SatelliteName.Equals(satelliteName, StringComparison.OrdinalIgnoreCase));
            if (satelliteToRemove == null)
            {
                MessageBox.Show($"未找到卫星 '{satelliteName}'！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }

            satellites.Remove(satelliteToRemove);
            return SaveAllSatellites(satellites);
        }

        /// <summary>
        /// 根据名称获取卫星轨道根数
        /// </summary>
        /// <param name="satelliteName">卫星名称</param>
        /// <returns>卫星轨道根数，未找到返回null</returns>
        public static SatelliteOrbitElement GetSatellite(string satelliteName)
        {
            if (string.IsNullOrWhiteSpace(satelliteName))
                return null;

            List<SatelliteOrbitElement> satellites = LoadAllSatellites();
            return satellites.FirstOrDefault(s => s.SatelliteName.Equals(satelliteName, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 获取所有卫星名称列表
        /// </summary>
        /// <returns>卫星名称列表</returns>
        public static List<string> GetAllSatelliteNames()
        {
            List<SatelliteOrbitElement> satellites = LoadAllSatellites();
            return satellites.Select(s => s.SatelliteName).OrderBy(name => name).ToList();
        }

        /// <summary>
        /// 将指定卫星的轨道根数写入orb_eph_orbit.ifm文件
        /// </summary>
        /// <param name="satelliteName">卫星名称</param>
        /// <param name="orbFilePath">orb_eph_orbit.ifm文件路径</param>
        /// <returns>写入是否成功</returns>
        public static bool WriteToOrbFile(string satelliteName, string orbFilePath)
        {
            try
            {
                SatelliteOrbitElement satellite = GetSatellite(satelliteName);
                if (satellite == null)
                {
                    MessageBox.Show($"未找到卫星 '{satelliteName}' 的轨道数据！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }

                string orbData = satellite.ToOrbDataString();
                File.WriteAllText(orbFilePath, orbData);
                
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"写入轨道文件失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 创建空的XML文件
        /// </summary>
        private static void CreateEmptyXmlFile()
        {
            try
            {
                SatelliteOrbitCollection emptyCollection = new SatelliteOrbitCollection();
                SaveAllSatellites(emptyCollection.Satellites);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"创建XML文件失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 检查XML文件是否存在，不存在则创建
        /// </summary>
        public static void EnsureXmlFileExists()
        {
            if (!File.Exists(XmlFilePath))
            {
                CreateEmptyXmlFile();
            }
        }
    }
}
