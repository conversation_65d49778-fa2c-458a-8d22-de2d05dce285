﻿using CrossLeading.Forms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Xml;
using MyListBox;

namespace CrossLeading.Cards
{
    public partial class DeviceConfig : UserControl
    {
        public DeviceConfig()
        {
            InitializeComponent();

            InitDevConfList();
            InitUACList();

            LoadDevConfList();
            LoadUacConfList();
        }

        /// <summary>
        /// 初始化设备配置信息列表视图
        /// </summary>
        private void InitDevConfList()
        {
            devConfList.View = View.Details;
            devConfList.FullRowSelect = true;
            devConfList.Columns.Add("设备名称", -2, HorizontalAlignment.Left);
            devConfList.Columns.Add("数据IP地址", 150, HorizontalAlignment.Left);
            devConfList.Columns.Add("收组播地址", 150, HorizontalAlignment.Left);
            devConfList.Columns.Add("收组播端口", 150, HorizontalAlignment.Left);
            devConfList.Columns.Add("发组播地址", 150, HorizontalAlignment.Left);
            devConfList.Columns.Add("发组播端口", 150, HorizontalAlignment.Left);
            devConfList.Columns.Add("纬度", 150, HorizontalAlignment.Left);
            devConfList.Columns.Add("经度", 150, HorizontalAlignment.Left);
            devConfList.Columns.Add("高程", 100, HorizontalAlignment.Left);
            devConfList.Columns.Add("是否为多波束装备", 200, HorizontalAlignment.Left);
        }

        /// <summary>
        /// 初始化设备配置信息列表视图
        /// </summary>
        private void InitUACList()
        {
            uacConfList.View = View.Details;
            uacConfList.FullRowSelect = true;
            uacConfList.Columns.Add("所属设备", 150, HorizontalAlignment.Left);
            uacConfList.Columns.Add("波束号", 150, HorizontalAlignment.Left);
            uacConfList.Columns.Add("UAC地址", -2, HorizontalAlignment.Left);
        }

        #region 设备配置区
        /// <summary>
        /// 从config.xml文件加载设备配置信息
        /// </summary>
        public void LoadDevConfList()
        {
            devConfList.Items.Clear();
            // 加载配置文件
            string xmlName = System.Windows.Forms.Application.StartupPath + "\\Config.xml";
            //初始化数据集合
            if (!System.IO.File.Exists(xmlName))
            {
                throw new Exception(string.Format("{0}", "路径不存在"));
            }
            XmlDocument doc = new XmlDocument();
            doc.Load(xmlName);
            XmlElement root = doc.DocumentElement;
            XmlNode node;

            foreach(XmlNode devConfNode in root)
            {
                if(devConfNode.NodeType == XmlNodeType.Element)
                {
                    node = root.SelectSingleNode(devConfNode.Name + "//RecvGroup");
                    string strDataIpAddr = node.SelectSingleNode("IpAddress").InnerText;
                    string strRecvGCAddr = node.SelectSingleNode("GroupAddress").InnerText;
                    string strRecvGCPort = node.SelectSingleNode("Port").InnerText;

                    node = root.SelectSingleNode(devConfNode.Name + "//SendGroup");
                    string strSendGCAddr = node.SelectSingleNode("GroupAddress").InnerText;
                    string strSendGCPort = node.SelectSingleNode("Port").InnerText;

                    node = root.SelectSingleNode(devConfNode.Name + "//Station");
                    string strLati = node.SelectSingleNode("B").InnerText;
                    string strLongi = node.SelectSingleNode("L").InnerText;
                    string strHeight = node.SelectSingleNode("H").InnerText;

                    node = root.SelectSingleNode(devConfNode.Name + "//isQD");
                    string isQD = node.InnerText;

                    ListViewItem devConfListItem = new ListViewItem(devConfNode.Name);
                    devConfListItem.SubItems.Add(strDataIpAddr);
                    devConfListItem.SubItems.Add(strRecvGCAddr);
                    devConfListItem.SubItems.Add(strRecvGCPort);
                    devConfListItem.SubItems.Add(strSendGCAddr);
                    devConfListItem.SubItems.Add(strSendGCPort);
                    devConfListItem.SubItems.Add(strLati);
                    devConfListItem.SubItems.Add(strLongi);
                    devConfListItem.SubItems.Add(strHeight);
                    devConfListItem.SubItems.Add(isQD);

                    devConfList.Items.Add(devConfListItem);
                }
            }
        }

        /// <summary>
        /// 保存配置信息到config.xml文件
        /// </summary>
        public void saveDevConfList()
        {
            // 加载配置文件
            string xmlName = System.Windows.Forms.Application.StartupPath + "\\Config.xml";
            //初始化数据集合
            if (!System.IO.File.Exists(xmlName))
            {
                throw new Exception(string.Format("{0}", "路径不存在"));
            }
            XmlDocument doc = new XmlDocument();
            XmlDeclaration dec = doc.CreateXmlDeclaration("1.0", "utf-8", null);
            doc.AppendChild(dec);
            XmlElement root = doc.CreateElement("Root");
            doc.AppendChild(root);

            foreach(ListViewItem devConf in devConfList.Items)
            {
                XmlElement devNameNode = doc.CreateElement(devConf.SubItems[0].Text);
                root.AppendChild(devNameNode);

                XmlElement recvGroupNode = doc.CreateElement("RecvGroup");
                XmlElement ipAddressNode = doc.CreateElement("IpAddress");
                ipAddressNode.InnerText = devConf.SubItems[1].Text;
                XmlElement groupAddressNode = doc.CreateElement("GroupAddress");
                groupAddressNode.InnerText = devConf.SubItems[2].Text;
                XmlElement portNode = doc.CreateElement("Port");
                portNode.InnerText = devConf.SubItems[3].Text;
                recvGroupNode.AppendChild(ipAddressNode);
                recvGroupNode.AppendChild(groupAddressNode);
                recvGroupNode.AppendChild(portNode);
                devNameNode.AppendChild(recvGroupNode);

                XmlElement sendGroupNode = doc.CreateElement("SendGroup");
                XmlElement ipAddressNode2 = doc.CreateElement("IpAddress");
                ipAddressNode2.InnerText = devConf.SubItems[1].Text;
                XmlElement groupAddressNode2 = doc.CreateElement("GroupAddress");
                groupAddressNode2.InnerText = devConf.SubItems[4].Text;
                XmlElement portNode2 = doc.CreateElement("Port");
                portNode2.InnerText = devConf.SubItems[5].Text;
                sendGroupNode.AppendChild(ipAddressNode2);
                sendGroupNode.AppendChild(groupAddressNode2);
                sendGroupNode.AppendChild(portNode2);
                devNameNode.AppendChild(sendGroupNode);

                XmlElement stationNode = doc.CreateElement("Station");
                XmlElement latiNode = doc.CreateElement("B");
                latiNode.InnerText = devConf.SubItems[6].Text;
                XmlElement longiNode = doc.CreateElement("L");
                longiNode.InnerText = devConf.SubItems[7].Text;
                XmlElement heightNode = doc.CreateElement("H");
                heightNode.InnerText = devConf.SubItems[8].Text;
                stationNode.AppendChild(latiNode);
                stationNode.AppendChild(longiNode);
                stationNode.AppendChild(heightNode);
                devNameNode.AppendChild(stationNode);

                XmlElement isQDNode = doc.CreateElement("isQD");
                isQDNode.InnerText = devConf.SubItems[9].Text;
                devNameNode.AppendChild(isQDNode);
            }
            doc.Save(xmlName);

            // 保存配置后重新加载引导界面设备选择的ComboBox项目
            Form1.newUI.InitCbItems();

        }

        /// <summary>
        /// 新建设备配置信息
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void devNewBtn_Click(object sender, EventArgs e)
        {
            DevEditForm devEditForm = new DevEditForm();
            devEditForm.ShowDialog();
            if (devEditForm.isOK)
            {
                ListViewItem devConfListItem = new ListViewItem(devEditForm.deviceName.Text);
                devConfListItem.SubItems.Add(devEditForm.dataIpAddr.Text);
                devConfListItem.SubItems.Add(devEditForm.recvGCAddr.Text);
                devConfListItem.SubItems.Add(devEditForm.recvGCPort.Text);
                devConfListItem.SubItems.Add(devEditForm.sendGCAddr.Text);
                devConfListItem.SubItems.Add(devEditForm.sendGCPort.Text);
                devConfListItem.SubItems.Add(devEditForm.lati.Text);
                devConfListItem.SubItems.Add(devEditForm.longi.Text);
                devConfListItem.SubItems.Add(devEditForm.height.Text);
                devConfListItem.SubItems.Add(devEditForm.chb_isQD.Text);
                devConfList.Items.Add(devConfListItem);

                saveDevConfList();
            }
        }

        /// <summary>
        /// 编辑设备信息
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void devEditBtn_Click(object sender, EventArgs e)
        {
            if (devConfList.SelectedItems.Count > 0)
            {
                ListViewItem item = devConfList.SelectedItems[0];
                DevEditForm devEditForm = new DevEditForm();
                foreach (Control c in devEditForm.Controls)
                {

                    if (c.GetType() == typeof(TextBox))
                    {
                        c.Text = item.SubItems[c.TabIndex - 1].Text;
                    }
                    if (c.GetType() == typeof(CheckBox))
                    {
                        (c as CheckBox).Checked = item.SubItems[c.TabIndex - 1].Text == "是" ? true : false;
                    }
                }
                devEditForm.ShowDialog();

                if (devEditForm.isOK)
                {
                    item.SubItems[0].Text = devEditForm.deviceName.Text;
                    item.SubItems[1].Text = devEditForm.dataIpAddr.Text;
                    item.SubItems[2].Text = devEditForm.recvGCAddr.Text;
                    item.SubItems[3].Text = devEditForm.recvGCPort.Text;
                    item.SubItems[4].Text = devEditForm.sendGCAddr.Text;
                    item.SubItems[5].Text = devEditForm.sendGCPort.Text;
                    item.SubItems[6].Text = devEditForm.lati.Text;
                    item.SubItems[7].Text = devEditForm.longi.Text;
                    item.SubItems[8].Text = devEditForm.height.Text;
                    item.SubItems[9].Text = devEditForm.chb_isQD.Checked ? "是" : "否";
                    //devConfList.Items.Add(item);
                    saveDevConfList();
                }
            }

        }

        /// <summary>
        /// 删除设备配置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void devDelBtn_Click(object sender, EventArgs e)
        {
            if (devConfList.SelectedItems.Count > 0)
            {
                ListViewItem item = devConfList.SelectedItems[0];
                devConfList.Items.Remove(item);
                saveDevConfList();
            }
        }
        #endregion 设备配置区


        #region uac配置区

        /// <summary>
        /// 从QDUacConfig.xml文件加载UAC配置信息
        /// </summary>
        public void LoadUacConfList()
        {
            uacConfList.Items.Clear();
            // 加载配置文件
            string xmlName = System.Windows.Forms.Application.StartupPath + "\\QDUacConfig.xml";
            //初始化数据集合
            if (!System.IO.File.Exists(xmlName))
            {
                throw new Exception(string.Format("{0}", "路径不存在"));
            }
            XmlDocument doc = new XmlDocument();
            doc.Load(xmlName);
            XmlElement root = doc.DocumentElement;
            XmlNode node;
            if (root.HasChildNodes)
            {
                foreach (XmlNode devNode in root)
                {
                    string devName = devNode.Name;
                    if (devNode.NodeType == XmlNodeType.Element) // 过滤掉注释节点
                    {
                        foreach(XmlNode devUacNode in devNode)
                        {
                            string devUacNo = devUacNode.Name;
                            string devUacAddr = devUacNode.InnerText;

                            ListViewItem uacConfListItem = new ListViewItem(devNode.Name);
                            uacConfListItem.SubItems.Add(devUacNo);
                            uacConfListItem.SubItems.Add(devUacAddr);

                            uacConfList.Items.Add(uacConfListItem);
                        }
                    }
                }
            }
            

            
        }

        /// <summary>
        /// 新建UAC配置信息
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uacNewBtn_Click(object sender, EventArgs e)
        {
            UACEditForm uacEditForm = new UACEditForm();
            uacEditForm.ShowDialog();
            if (uacEditForm.isOK)
            {
                ListViewItem uacConfListItem = new ListViewItem(uacEditForm.cb_dev.Text);
                uacConfListItem.SubItems.Add(uacEditForm.tb_beamNo.Text);
                uacConfListItem.SubItems.Add(uacEditForm.tb_uacNo.Text);
                uacConfList.Items.Add(uacConfListItem);

                saveUACConfList();
            }
        }

        /// <summary>
        /// 编辑UAC信息
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uacEditBtn_Click(object sender, EventArgs e)
        {
            if (uacConfList.SelectedItems.Count > 0)
            {
                ListViewItem item = uacConfList.SelectedItems[0];
                UACEditForm uacEditForm = new UACEditForm();
                foreach (Control c in uacEditForm.Controls)
                {

                    if (c.GetType() == typeof(TextBox))
                    {
                        c.Text = item.SubItems[c.TabIndex - 1].Text;
                    }
                    if (c.GetType() == typeof(ComboBox))
                    {
                        (c as ComboBox).Text = item.SubItems[c.TabIndex - 1].Text;
                    }
                }
                uacEditForm.ShowDialog();

                if (uacEditForm.isOK)
                {
                    item.SubItems[0].Text = uacEditForm.cb_dev.Text;
                    item.SubItems[1].Text = uacEditForm.tb_beamNo.Text;
                    item.SubItems[2].Text = uacEditForm.tb_uacNo.Text;
                    saveUACConfList();
                }
            }
        }

        /// <summary>
        /// 删除设备配置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uacDelBtn_Click(object sender, EventArgs e)
        {
            if (uacConfList.SelectedItems.Count > 0)
            {
                ListViewItem item = uacConfList.SelectedItems[0];
                uacConfList.Items.Remove(item);
                saveUACConfList();
            }
        }

        /// <summary>
        /// 保存配置信息到config.xml文件
        /// </summary>
        public void saveUACConfList()
        {
            // 加载配置文件
            string xmlName = System.Windows.Forms.Application.StartupPath + "\\QDUacConfig.xml";
            //初始化数据集合
            if (!System.IO.File.Exists(xmlName))
            {
                throw new Exception(string.Format("{0}", "路径不存在"));
            }
            XmlDocument doc = new XmlDocument();
            XmlDeclaration dec = doc.CreateXmlDeclaration("1.0", "utf-8", null);
            doc.AppendChild(dec);
            XmlElement root = doc.CreateElement("Root");
            doc.AppendChild(root);
            XmlNode node;

            for (int i = 0; i < uacConfList.Items.Count; i ++)
            {
                ListViewItem uacConf = uacConfList.Items[i];
                string devName = uacConf.SubItems[0].Text;
                // 如果存在名为devName的节点，则向其中插入UAC地址
                // 否则新建一个名为devName的节点，然后向其中插入UAC地址
                XmlNode devNode;
                if (root.SelectNodes(devName).Count > 0)
                {
                    devNode = root.SelectSingleNode(devName);
                }
                else
                {
                    devNode = doc.CreateElement(devName);
                    root.AppendChild(devNode);
                }

                string beamNo = uacConf.SubItems[1].Text;
                XmlElement beamNoNode = doc.CreateElement(beamNo);
                beamNoNode.InnerText = uacConf.SubItems[2].Text;
                devNode.AppendChild(beamNoNode);
            }
            doc.Save(xmlName);
            //// 保存配置后重新加载引导界面设备选择的ComboBox项目
            //foreach (Form frm in Application.OpenForms)
            //{
            //    if (frm.Name == "Form1")
            //    {
            //        Form1 frm1 = frm as Form1;
            //        frm1.newUI.InitCbItems();
            //    }
            //}
        }


        #endregion uac配置区

        /// <summary>
        /// 加载设备配置信息列表
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void DeviceConfig_Load(object sender, EventArgs e)
        {
            LoadDevConfList();
            LoadUacConfList();
        }
    }
}
