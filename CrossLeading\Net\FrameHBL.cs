﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace CrossLeading.Net
{
    /// <summary>
    /// 雷达7D数据
    /// </summary>
    public class FrameHBL
    {
        public uint _aim_num;
        public TarData[] _tarData;
        public FrameHBL()
        {

        }
        public static FrameHBL Parse(byte[] data, int ops)
        {
            FrameHBL frame = new FrameHBL();
            frame._aim_num = (uint)data[3];
            frame._tarData = new TarData[frame._aim_num];
            frame._tarData[0] = TarData.Parse(data, ops+4);
            return frame;
        }
    }

    public class TarData
    {
        public uint nb_50ms;
        public uint time_s;
        public uint time_m;
        public uint time_h;
        public byte zd;

        public byte[] fd = new byte[3];

        /// <summary>
        /// 量化单位 1.953126 米 无符号
        /// </summary>
        public double r;
        /// <summary>
        /// 量化单位 0.001373291 度 无符号
        /// </summary>
        public double a;
        /// <summary>
        /// 量化单位 0.001373291 度 无符号
        /// </summary>
        public double e;
        /// <summary>
        /// 量化单位 0.01度 符号
        /// </summary>
        public byte dalt_a;
        /// <summary>
        /// 量化单位 0.01度 符号
        /// </summary>
        public byte dalt_e;


        /// <summary>
        /// 0.5dB 无符号
        /// </summary>
        public byte agc;

        /// <summary>
        /// 浮点数 4byte
        /// </summary>
        public float fRcs;
        public static TarData Parse(byte[] data, int ops)
        {
            TarData frame = new TarData();
            
            frame.nb_50ms = (uint)(data[ops + 1]);
            frame.time_s = getInt(data[ops + 2]);
            byte tmp = data[ops + 3];
            tmp = (byte)((tmp << 1)&0xFE);
            tmp = (byte)(tmp | (byte)(data[ops + 2] >> 7));
            frame.time_m = getInt(tmp);

            tmp = data[ops + 4];
            tmp = (byte)((tmp << 2) & 0xFE);
            tmp = (byte)(tmp | (byte)(data[ops + 3] >> 6));
            frame.time_h = getInt(tmp);

            frame.zd = data[ops + 5];

            UInt32 tmp1;
            tmp1 = App.getUInt24(data, ops + 9);
            frame.r = tmp1 * 1.953125;
            tmp1 = App.getUInt24(data, ops + 12);
            frame.a = tmp1 * 360.0 / Math.Pow(2, 18);
            tmp1 = App.getUInt24(data, ops + 15);
            frame.e = tmp1 * 360.0 / Math.Pow(2, 18);
            Console.WriteLine("Time: " + frame.time_h + ":" + frame.time_m + ":" + frame.time_s + "." + frame.nb_50ms * 50 + " \t  A: " + frame.a + "   E: " + frame.e + "  R:" + frame.r);
            return frame;
        }
        public static uint getInt(byte b)
        {
            uint tmp = 0;
            tmp = (uint)((b >> 4 )& 0x7) * 10;
            tmp += (uint)(b &0xF);
            return tmp;
        }
    }


    /// <summary>
    /// 字节转换
    /// </summary>
    public class App
    {
        #region  字节转换
        public static UInt64 getUInt64(byte[] data, int ops)
        {
            UInt64 result = 0;
            result = data[ops + 7];
            result <<= 8;
            result |= data[ops + 6];
            result <<= 8;
            result |= data[ops + 5];
            result <<= 8;
            result |= data[ops + 4];
            result <<= 8;
            result |= data[ops + 3];
            result <<= 8;
            result |= data[ops + 2];
            result <<= 8;
            result |= data[ops + 1];
            result <<= 8;
            result |= data[ops];
            return result;
        }
        public static Int64 getInt64(byte[] data, int ops)
        {
            Int64 result = 0;
            result = data[ops + 7];
            result <<= 8;
            result |= data[ops + 6];
            result <<= 8;
            result |= data[ops + 5];
            result <<= 8;
            result |= data[ops + 4];
            result <<= 8;
            result |= data[ops + 3];
            result <<= 8;
            result |= data[ops + 2];
            result <<= 8;
            result |= data[ops + 1];
            result <<= 8;
            result |= data[ops];
            return result;
        }
        public static UInt32 getUInt32(byte[] data, int ops)
        {
            UInt32 result = 0;
            result = data[ops + 3];
            result <<= 8;
            result |= data[ops + 2];
            result <<= 8;
            result |= data[ops + 1];
            result <<= 8;
            result |= data[ops];
            return result;
        }


        public static UInt32 getUInt24(byte[] data, int ops)
        {
            UInt32 result = 0;
            result = 0;
            result <<= 8;
            result |= data[ops + 2];
            result <<= 8;
            result |= data[ops + 1];
            result <<= 8;
            result |= data[ops];
            return result;
        }


        public static Int32 getInt32(byte[] data, int ops)
        {
            Int32 result = 0;
            result = data[ops + 3];
            result <<= 8;
            result |= data[ops + 2];
            result <<= 8;
            result |= data[ops + 1];
            result <<= 8;
            result |= data[ops];
            return result;
        }
        public static UInt16 getUInt16(byte[] data, int ops)
        {
            UInt16 result = 0;
            result |= data[ops + 1];
            result <<= 8;
            result |= data[ops];
            return result;
        }
        public static Int16 getInt16(byte[] data, int ops)
        {
            //byte[] tmp = new byte[2];
            // tmp[0] = data[ops];
            //tmp[1] = data[ops + 1];

            Int16 result = 0;
            //result = Convert.ToInt16(tmp);
            result |= data[ops + 1];
            result <<= 8;
            result |= data[ops];
            return result;
        }

        #endregion
    }
   
}
