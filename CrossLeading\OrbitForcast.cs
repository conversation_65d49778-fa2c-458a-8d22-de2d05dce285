﻿/// <summary>
/// 版权所有: (c) 2012, XSCC All Right Reserved 
/// 文件名称: OrbitForcast.cs 
/// 功能描述: 近地卫星轨道计算及坐标变换
/// 创建信息: 张峰 2012-04-07
/// </summary>
/// 
/// 详细功能：
/// 1. 轨道推算相关函数
/// 2. 时间变换相关函数，静态，可不声明对象而使用，如 
///     Jd = OrbitForcast.GetJD(2011, 8, 3, false);
/// 3. 坐标变换相关函数，静态。
/// 

using System;

public class OrbitForcast
{

    public const  double dEarthAdius = 6378140.0;			// 地球半径(米)
    public const double dSatelliteTime = 806.8116339;		// 地球人卫时间(s)
    public const double dESQU = 0.006694385000;			// 地球子午线偏心率的平方
    public const double dStarTimeRate = 0.000072921151467;	// 恒星时变率
    public const double dC = 299792458.0;                  // 光速(m/s)

    
    int Jd;             //根数积日	t01, 相对于1950年的积日
    uint Js;            //根数积秒	t02, 0.1毫秒
    double dA, dOrbitA;  //半长轴	a, 米
    double dE;          //偏心率	e, 
    double dI, dOrbitI;  //倾角		i, 度
    double dW, dOrbitW;  //升交点赤经 Ω, 度    RAAN
    double dQ, dOrbitQ;  //近地点幅角 ω, 度    Argument of Perigee
    double dM, dOrbitM;  //平近点角	M, 度

    double[] m_dVectorP;  //近地点方向单位矢量P
    double[] m_dVectorQ;  //半通径方向单位矢量Q
    double dN;            //平均角速度

    bool m_bFirstCalculate; //推算过程中的首次计算时,true
    int jdIntegr;   //积分时间
    uint jsIntegr;
    double[] drInter;   //位置矢量 用于积分
    double[] dvInter;   //速度矢量 用于积分

    public OrbitForcast()
	{
        m_dVectorP = new double[3];
        m_dVectorQ = new double[3];
        m_bFirstCalculate = false;
        drInter = new double[3];
        dvInter = new double[3];
	}
    
    // 设置轨道的瞬时根数
    public int SetOrbit(int iJD, uint iJS, double a, double e, double i, double W, double Q, double M)
    {
        Jd = iJD;
        Js = iJS;
        dA = a;
        dE = e;
        dI = i;
        dW = W;
        dQ = Q;
        dM = M;
        // (1) 纲量变换
        dW -= 0.64175;  //2000年预报修正值
        TransUint();
        // (2) (3) 计算近地点矢量P、半通径矢量Q、平均角速度
        CalculatePQ();
        m_bFirstCalculate = true;

        return 0;
    }
 
    /// <summary>
    /// 计算轨道目标的位置（测站球坐标系）
    /// </summary>
    /// <param name="dDeltSec"></param>
    /// <param name="BLHsat"></param>
    /// <param name="RAE"></param>
    /// <returns></returns>
    public int CalculateOrbitRAE(double dDeltSec, double[] BLHsat, out double[] RAE)
    {        
        RAE = new double[6];
        double[] Rxyz;
        double[] Rv;
        double[] RVxyz = new double[6];
        
        CalculateSatInStation(dDeltSec, BLHsat, out Rxyz, out Rv);  //计算出目标的测站直角坐标

        // 位置和速度合并为1个向量
        for (int i = 0; i < 3; i++)
        {
            RVxyz[i] = Rxyz[i];
            RVxyz[i + 3] = Rv[i];
        }
        RAE = staRxyz2RAE(RVxyz);        //测站直角坐标 --> 测站球坐标
        return 0;
    }
    /// <summary>
    /// 计算轨道目标的位置（测站直角坐标系）
    /// </summary>
    /// <param name="dDeltSec"></param>
    /// <param name="BLHsat"></param>
    /// <param name="RVxyz"></param>
    /// <returns></returns>
    public int CalculateOrbitRxyz(double dDeltSec, double[] BLHsat, out double[] RVxyz)
    {
        RVxyz = new double[6];
        double[] Rxyz;
        double[] Rv;        

        CalculateSatInStation(dDeltSec, BLHsat, out Rxyz, out Rv);  //计算出目标的测站直角坐标
        // 位置和速度合并为1个向量
        for (int i = 0; i < 3; i++)
        {
            RVxyz[i] = Rxyz[i];
            RVxyz[i + 3] = Rv[i];
        }
        return 0;
    }
    /// <summary>
    /// 计算目标在测站直角坐标系下的位置和速度
    /// </summary>
    /// <param name="dDeltSec"></param>
    /// <param name="BLHsat"></param>
    /// <param name="Rxyz"></param>
    /// <param name="Rv"></param>
    /// <returns></returns>
    public int CalculateSatInStation(double dDeltSec, double[] BLHsat, out double[] Rxyz, out double[] Rv)
    {
        Rxyz = new double[3];
        Rv = new double[3];
        double dEt; //偏近点角
        double[] Exyz = new double[3];
        double[] Ev = new double[3]; 
        double[] ESta;
        double[] EvSta;
        double dSt;
        int jdCur;
        uint jsCur;

        // 由相对根数的时间转换为积日积秒
        DateTime dtRoot = JdJs2DateTime(Jd, Js);
        DateTime dtCur = dtRoot.AddSeconds(dDeltSec);
        jdCur = GetJD(dtCur.Year, dtCur.Month, dtCur.Day, true);
        jsCur = GetJS(dtCur.Hour, dtCur.Minute, dtCur.Second, dtCur.Millisecond);


        if (m_bFirstCalculate)
        {
            // (4) (5) 计算给定时间的偏近点角E(t)
            dEt = CalculateE(dDeltSec);
            // (6) (7) 计算轨道坐标系中的位置和速度      
            CalculateExyz(dEt, out drInter, out dvInter);
            m_bFirstCalculate = false;
                                  
            jdIntegr = jdCur;   //设置积分初始时间
            jsIntegr = jsCur;
        }
        // 积分
        Integrate(jdCur, jsCur, ref drInter, ref dvInter);
       
        // (8) 计算格林尼治恒星时
        double lonL = BLHsat[1] * Math.PI / 180;
        
        dSt = StarLocalTime(jdCur, jsCur, lonL);
        // 计算测站在轨道坐标系中的位置速度
        CalculateStationInOrbitRV(jdCur, jsCur, BLHsat, out ESta, out EvSta);
               

        // 矢量差
        double[] dLocation = new double[3];
        double[] dRate = new double[3];
        for (int i = 0; i < 3; i++)
        {
            // 恢复标准单位量纲
            Exyz[i] = drInter[i] * dEarthAdius;
            Ev[i] = dvInter[i] * (dEarthAdius / dSatelliteTime);

            //计算在轨道坐标系中的矢量差
            dLocation[i] = Exyz[i] - ESta[i];
            dRate[i] = Ev[i] - EvSta[i];
        }
        ////////////////////////////////////////////////////////////////////
        // 计算卫星在测站坐标系中的位置
        double[] coff = new double[9];
        double[] dAthwartCoff = new double[9];
        
        MakeAthwCoff(dSt, BLHsat[0] * Math.PI / 180, out dAthwartCoff); 
        MatProd(dAthwartCoff, dLocation, 3, 3, 1, out Rxyz);
        // 计算卫星在测站坐标系中的速度
        MakeCoff2(dSt, BLHsat[0] * Math.PI / 180, out coff);
        double[] tempR = new double[3];
        MatProd(coff, Rxyz, 3, 3, 1, out tempR);
        MatProd(dAthwartCoff, dRate, 3, 3, 1, out Rv);
        for (int i = 0; i < 3; i++)
        {
            Rv[i] -= tempR[i];
        }        
        return 0;

    }
    /// <summary>
    /// 计算测站在轨道坐标系下的位置和速度
    /// </summary>
    /// <param name="jd"></param>
    /// <param name="js"></param>
    /// <param name="BLHsat"></param>
    /// <param name="Exyz"></param>
    /// <param name="Ev"></param>
    /// <returns></returns>
    public int CalculateStationInOrbitRV(int jd, uint js, double[] BLHsat, out double[] Exyz, out double[] Ev)
    {
        Exyz = new double[3];
        Ev = new double[3];
        double LatB = BLHsat[0] * Math.PI / 180;    //纬度 rad
        double lonL = BLHsat[1] * Math.PI / 180;    //经度 rad
        double dNc = dEarthAdius / Math.Sqrt(1 - dESQU * Math.Pow(Math.Sin(LatB), 2));

	    ////////////////////////////////////////////////////////////////////
	    // 计算地方恒星时, 并转换成弧度单位       
        double dSci = StarLocalTime(jd, js, lonL);
	    ////////////////////////////////////////////////////////////////////
	    // 计算测站在轨道坐标系中的位置
        Exyz[0] = (dNc + BLHsat[2]) * Math.Cos(LatB) * Math.Cos(dSci) ;
        Exyz[1] = (dNc + BLHsat[2]) * Math.Cos(LatB) * Math.Sin(dSci) ;
        Exyz[2] = (dNc * (1 - dESQU) + BLHsat[2]) * Math.Sin(LatB) ;

	    ////////////////////////////////////////////////////////////////////
	    // 计算测站在轨道坐标系中的速度
        Ev[0] = -dStarTimeRate * Exyz[1] ;
        Ev[1] = dStarTimeRate * Exyz[0] ;
        Ev[2] = 0.0;
        
	    return 0;
    }
    
    /// <summary>
    /// 构造转换矩阵[(Sci)(Bc)(A0)]
    /// </summary>
    /// <param name="dSci"></param>
    /// <param name="dBc"></param>
    /// <param name="dCoff"></param>
    public void MakeCoff2(double dSci, double dBc, out double[] dCoff)
    {
        dCoff = new double[9];
        dCoff[0] = dStarTimeRate * Math.Sin(dSci) * Math.Sin(dBc);
        dCoff[1] = -dStarTimeRate * Math.Sin(dSci) * Math.Cos(dBc);
        dCoff[2] = -dStarTimeRate * Math.Cos(dSci);

        dCoff[3] = -dStarTimeRate * Math.Cos(dSci) * Math.Sin(dBc);
        dCoff[4] = dStarTimeRate * Math.Cos(dSci) * Math.Cos(dBc);
        dCoff[5] = -dStarTimeRate * Math.Sin(dSci);

	    dCoff[6] = 0.0;
	    dCoff[7] = 0.0;
	    dCoff[8] = 0.0;
    }

    /// <summary>
    /// 构造转换矩阵[(Sci)(Bc)(A0)]的逆
    /// </summary>
    /// <param name="dSci"></param>
    /// <param name="dBc"></param>
    /// <param name="dCoff"></param>
    public void MakeAthwCoff(double dSci, double dBc, out double[] dAthwartCoff)
    {
        dAthwartCoff = new double[9];
        dAthwartCoff[0] = -Math.Cos(dSci) * Math.Sin(dBc);
        dAthwartCoff[1] = -Math.Sin(dSci) * Math.Sin(dBc);
        dAthwartCoff[2] = Math.Cos(dBc);

        dAthwartCoff[3] = Math.Cos(dSci) * Math.Cos(dBc);
        dAthwartCoff[4] = Math.Sin(dSci) * Math.Cos(dBc);
	    dAthwartCoff[5] =  Math.Sin(dBc);

        dAthwartCoff[6] = -Math.Sin(dSci);
        dAthwartCoff[7] = Math.Cos(dSci);
	    dAthwartCoff[8] =  0.0;

    }
    /// <summary>
    /// 计算两个矩阵的乘积  
    /// </summary>
    /// <param name="dMatrix1">l*m阶矩阵</param>
    /// <param name="dMatrix2">m*n阶矩阵</param>
    /// <param name="l"></param>
    /// <param name="m"></param>
    /// <param name="n"></param>
    /// <param name="dMatrix3">l*n阶矩阵</param>
    public void MatProd(double[] dMatrix1, double[] dMatrix2, int l, int m, int n, out double[] dMatrix3)
    {
        dMatrix3 = new double[3];
        for (int i = 0; i < l; i++)
        {
            for (int j = 0; j < n; j++)
            {
                dMatrix3[i * n + j] = 0.0;
                for (int k = 0; k < m; k++)
                {
                    dMatrix3[i * n + j] += dMatrix1[i * m + k] * dMatrix2[k * n + j];
                }
            }
        }
    }
        
 /// <summary>
 /// 积分运算
 /// </summary>
 /// <param name="nJd">积日</param>
 /// <param name="dwJs">积秒(单位0.1ms)</param>
 /// <param name="dR">位置(归一化)</param>
 /// <param name="dV">速度(归一化)</param>
    public void Integrate(int nJd, uint dwJs, ref double[] dR, ref double[] dV)
    {

        ////////////////////////////////////////////////////////////////////
	    // 由开始时间积分到下一时刻的时间间隔
	    double dDeltaTime = (double)(nJd - jdIntegr) * 86400 + ((double)dwJs - (double)jsIntegr) / 10000.0;
	    int nDeltaTime = (int)(dDeltaTime);
        ////////////////////////////////////////////////////////////////////
        // 如果时间间隔小于0，推根数，计算偏近点角而后计算
        // 目标在轨道坐标系中的位置和速度
        if (dDeltaTime < 0)
        {
            ////////////////////////////////////////////////////////////////////
            // 计算偏近点角
            double deltSec2root = (double)(nJd - Jd) * 86400 + ((double)dwJs - (double)Js) / 10000.0;
            double dPe = CalculateE(deltSec2root);

            ////////////////////////////////////////////////////////////////////
            // 求目标在轨道坐标系中的位置和速度            
            CalculateExyz(dPe, out drInter, out dvInter);

            ////////////////////////////////////////////////////////////////////
            // 设置积分时间
            jdIntegr = nJd;
            jsIntegr = dwJs;

            return;
        }
        ////////////////////////////////////////////////////////////////////
        // 时间间隔的小数部分（单位1秒）
        double dResidue = dDeltaTime - Math.Floor(dDeltaTime);
        int nNum = 0;
   
        ////////////////////////////////////////////////////////////////////
        // 对不足一秒的时间进行运动积分
        if (dResidue > 0)
            Ruge_Kuta(dResidue, ref dR, ref dV);

        ////////////////////////////////////////////////////////////////////
        // 对整秒秒的时间进行运动积分
        if (nDeltaTime <= 0)
            return;

        ////////////////////////////////////////////////////////////////////
        // 大于100秒，步长为100秒
        nNum = nDeltaTime / 100;
        if (nNum > 0)
        {
            for (int i = 0; i < nNum; i++)
                Ruge_Kuta(100, ref dR, ref dV);
        }

        ////////////////////////////////////////////////////////////////////
        // 小于100秒大于10秒，步长为10秒
        nNum = (nDeltaTime % 100) / 10;
        if (nNum > 0)
        {
            for (int i = 0; i < nNum; i++)
                Ruge_Kuta(10, ref dR, ref dV);
        }

        ////////////////////////////////////////////////////////////////////
        // 小于10秒，步长为1.0秒
        nNum = nDeltaTime % 10;
        for (int i = 0; i < nNum; i++)
            Ruge_Kuta(1, ref dR, ref dV);        

        ////////////////////////////////////////////////////////////////////
        // 更新积分时间
        jdIntegr = nJd;
        jsIntegr = dwJs;
    }
    /// <summary>
    /// 龙格.库塔积分
    /// </summary>
    /// <param name="dStep">步长(秒)</param>
    /// <param name="dR">位置</param>
    /// <param name="dV">速度</param>
    public void Ruge_Kuta(double dStep, ref double[] dR, ref double[] dV)
    {
        double[] X0 = new double[6];
        double[] Xc = new double[6];
        double[] Xp = new double[6];
        double[] A = new double[4];
        double[] X = new double[6];        
        	    
        A[0] = dStep / 2.0 / dSatelliteTime;
        A[1] = A[0];
        A[2] = dStep / dSatelliteTime;
        A[3] = dStep / dSatelliteTime;

        for (int i = 0; i < 3; i++)
        {
            X[i] = dR[i];
            X[i + 3] = dV[i];

            X0[i] = dR[i];
            X0[i + 3] = dV[i];
        }

        Xp = dpRIGHT(X);

        for (int k = 0; k < 3; k++)
        {
            for (int i = 0; i < 6; i++)
            {
                Xc[i] = X0[i] + A[k] * Xp[i];
                X[i] = X[i] + A[k + 1] * Xp[i] / 3.0;                
            }
            Xp = dpRIGHT(Xc);
        }
        for (int i = 0; i < 6; i++)
        {
            X[i] = X[i] + A[0] * Xp[i] / 3.0;            
        }
        for (int i = 0; i < 3; i++)
        {
            dR[i] = X[i];
            dV[i] = X[i + 3];
        }
    }
    /// <summary>
    /// Right()子函数
    /// </summary>
    /// <param name="Ein">空间矢量,6*1</param>
    /// <returns></returns>
    public double[] dpRIGHT(double[] Ein)
    {
        double[] Eout = new double[6];

        double dJ2 = 1.0826269e-3;
        double dR;
        dR = Math.Sqrt(Ein[0] * Ein[0] + Ein[1] * Ein[1] + Ein[2] * Ein[2]);
        Eout[0] = Ein[3];
        Eout[1] = Ein[4];
        Eout[2] = Ein[5];
        Eout[3] = -Ein[0] / (dR * dR * dR) * (1 - dJ2 / (dR * dR) * (7.5 * (Ein[2] / dR) * (Ein[2] / dR) - 1.5));
        Eout[4] = -Ein[1] / (dR * dR * dR) * (1 - dJ2 / (dR * dR) * (7.5 * (Ein[2] / dR) * (Ein[2] / dR) - 1.5));
        Eout[5] = -Ein[2] / (dR * dR * dR) * (1 - dJ2 / (dR * dR) * (7.5 * (Ein[2] / dR) * (Ein[2] / dR) - 4.5));

        return Eout;
    }


    // (1) 纲量变换
    private int TransUint()
    {
        dOrbitA = dA / dEarthAdius;
        dOrbitI = dI * Math.PI / 180.0;
        dOrbitW = dW * Math.PI / 180.0;
        dOrbitQ = dQ * Math.PI / 180.0;
        dOrbitM = dM * Math.PI / 180.0;
        return 0;
    }
    // (2) (3) 计算近地点矢量P、半通径矢量Q、平均角速度
    private int CalculatePQ()
    {
        // 计算近地点方向单位矢量P
        m_dVectorP[0] = Math.Cos(dOrbitW) * Math.Cos(dOrbitQ) - Math.Sin(dOrbitW) * Math.Sin(dOrbitQ) * Math.Cos(dOrbitI);
        m_dVectorP[1] = Math.Cos(dOrbitQ) * Math.Sin(dOrbitW) + Math.Sin(dOrbitQ) * Math.Cos(dOrbitW) * Math.Cos(dOrbitI);
        m_dVectorP[2] = Math.Sin(dOrbitQ) * Math.Sin(dOrbitI);
        // 计算半通径方向单位矢量Q
        m_dVectorQ[0] = -Math.Sin(dOrbitQ) * Math.Cos(dOrbitW) - Math.Cos(dOrbitQ) * Math.Sin(dOrbitW) * Math.Cos(dOrbitI);
        m_dVectorQ[1] = -Math.Sin(dOrbitQ) * Math.Sin(dOrbitW) + Math.Cos(dOrbitQ) * Math.Cos(dOrbitW) * Math.Cos(dOrbitI);
        m_dVectorQ[2] = Math.Cos(dOrbitQ) * Math.Sin(dOrbitI);
        // 计算平均角速度
        dN = Math.Pow(dOrbitA, -3.0 / 2);

        return 0;
    }

    /// <summary>
    /// (4) (5) 计算给定时间的偏近点角E(t)
    /// </summary>
    /// <param name="dDeltTime">当前积秒 - 瞬根积秒 （单位为秒</param>
    /// <returns></returns>
    private double CalculateE(double dDeltTime)
    {
        // (4) t时刻的平近点角M(t)
   //     double dDeltTime = (JdCur - Jd) * 86400.0 + (JsCur - Js) / 10000.0;
        double dMt = dOrbitM + dN * dDeltTime / dSatelliteTime;
        //////////////////////////////////////////////////////////////////////////
        // (5) 迭代法计算偏近点角E(t)
        double[] dPe = new double[50];
        double dEE = 0;
        double dEt;

        dPe[0] = dMt;
        int j = 0;
        do
        {
            dEE = dPe[j];
            if (++j >= 50)
            {
                //////////////////////////////////////////////////////////////////////
                // 近似公式计算偏近点角
                dPe[j] = dMt + dE * Math.Sin(dMt) + 0.5 * Math.Pow(dE, 2) * Math.Sin(2 * dMt);
                dEt = dPe[j];
                break;
            }
            dPe[j] = dEE - (dEE - dE * Math.Sin(dEE) - dMt) / (1 - dE * Math.Cos(dEE));
            dEt = dPe[j];
        }
        while (!((Math.Abs(dEt - dEE) < Math.Pow(10.0, -10)) ||
            (Math.Abs(dEt - dEE + 2 * Math.PI) < Math.Pow(10.0, -10)) ||
            (Math.Abs(dEt - dEE - 2 * Math.PI) < Math.Pow(10.0, -10))));
        return dEt;
    }
    //(6) (7)  计算目标在轨道坐标系下的位置和速度
    private int CalculateExyz(double dEt, out double[] Exyz, out double[] Ev)
    {
        // (6) 计算轨道坐标系中的位置和速度
        Exyz = new double[3];
        Ev = new double[3];

        double coffR1 = dOrbitA * (Math.Cos(dEt) - dE);
        double coffR2 = dOrbitA * Math.Sqrt(1 - dE * dE) * Math.Sin(dEt);
        double coffV1 = Math.Sqrt(dOrbitA) / dOrbitA / (1 - dE * Math.Cos(dEt));
        double coffV2 = -Math.Sin(dEt);
        double coffV3 = Math.Sqrt(1 - dE * dE) * Math.Cos(dEt);

        for (int i = 0; i < 3; i++)
        {
            // 计算位置向量
            Exyz[i] = coffR1 * m_dVectorP[i] + coffR2 * m_dVectorQ[i];
            // 计算速度向量
            Ev[i] = coffV1 * (coffV2 * m_dVectorP[i] + coffV3 * m_dVectorQ[i]);
        }

        return 0;
    }
    public int SetFirstCalc(bool isFrst)
    {
        m_bFirstCalculate = isFrst;
        return 0;
    }
    /// <summary>
    /// 计算轨道周期
    /// </summary>
    /// <returns>周期（s）</returns>
    public double GetCyc()
    {
        double CycSec = 0;
        if (dN != 0)
        {
            CycSec = 2 * Math.PI / dN * dSatelliteTime;
        }
        return CycSec;
    }
    

#region 时间变换相关
    /// <summary>
    /// 年月日-->积日
    /// 功能：根据年、月、日计算积日，并输出
    ///	输入：b2000 = TRUE: 相对于2000年标志，否则为对应于1950
    /// </summary>
    /// <param name="nYear"></param>
    /// <param name="nMonth"></param>
    /// <param name="nDay"></param>
    /// <param name="b2000"></param>
    /// <returns></returns>
    static public int GetJD(int nYear, int nMonth, int nDay, bool b2000)
    {
        int nResult = 0;
        if (nYear < 1950)
            return 0;
        if (nMonth < 3)
        {
            nMonth += 12;
            nYear--;
        }
        nYear = (int)((nYear - 1900) * 365.25);
        nMonth = (int)((nMonth * 153.0 - 1.5) / 5);
        nResult = nYear + nMonth + nDay;

        if (b2000)
            return (nResult - 18294 - 18262);
        else
            return (nResult - 18294);
    }
    /// <summary>
    /// 积日-->年月日
    /// </summary>
    /// <param name="nJD">积日,相对2000.0.0</param>
    /// <param name="nYear">年</param>
    /// <param name="nMonth">月</param>
    /// <param name="nDay">日</param>
    static public void JD2YMD(int nJD, out int nYear, out int nMonth, out int nDay)
    {
        int l, m, n, i, j;
        l = nJD + 18294 + 18262;    //--更改
        i = (int)Math.Floor((l * 4 - 365.0) / 1461.0);
        j = (int)(l - Math.Floor(i * 365.251));
        if (j < 92)
        {
            i = i - 1; j = j + 365;
        }
        m = (int)Math.Floor((j * 5 + 1.5) / 153.0);
        n = (int)(j - Math.Floor((m * 153.0 - 1.5) / 5.0));
        if (m > 12)
        {
            m = m - 12; i = i + 1;
        }
        nYear = 1900 + i;
        nMonth = m;
        nDay = n;
    }
    /// <summary>
    /// 时分秒毫秒-->积秒
    /// 功能：根据时、分、秒、毫秒计算当日积秒，并输出
    /// 输出：积秒，以0.1毫秒为单位
    /// </summary>
    /// <param name="nHour"></param>
    /// <param name="nMinute"></param>
    /// <param name="nSecond"></param>
    /// <param name="dMS"></param>
    /// <returns></returns>
    static public uint GetJS(int nHour, int nMinute, int nSecond, double dMS)
    {
        uint uResult = 0;
        uResult = (uint)((((nHour * 60 + nMinute) * 60 + nSecond) * 1000 + dMS) * 10);
        return uResult;
    }
    /// <summary>
    /// 积秒-->时分秒毫秒
    /// </summary>
    /// <param name="dJS">积秒（0.1ms)</param>
    /// <param name="nHour">时</param>
    /// <param name="nMinute">分</param>
    /// <param name="nSecond">秒</param>
    /// <param name="dMS">毫秒(小数)</param>
    /// <returns>返回0:计算正常</returns>
    static public int JS2HMS(uint dJS, out int nHour, out int nMinute, out int nSecond, out double dMS)
    {
        if (dJS < 0)
        {
            nHour = 0;
            nMinute = 0;
            nSecond = 0;
            dMS = 0.0;
            return -1;
        }

        double dTemp = dJS / 10;
        int nTemp = (int)dTemp;
        nHour = nTemp / 3600000;
        nMinute = (nTemp % 3600000) / 60000;
        nSecond = (nTemp % 60000) / 1000;
        dMS = dJS % 10000;
        return 0;
    }
    /// <summary>
    /// 积日积秒 --> DateTime
    /// </summary>
    /// <param name="nJD"></param>
    /// <param name="dJS"></param>
    /// <returns></returns>
    static public DateTime JdJs2DateTime(int nJD, uint dJS )
    {
        int nYear = 0;
        int nMonth = 0;
        int nDay = 0;
        int nHour;
        int nMinute;
        int nSecond;
        double dMS;

        JD2YMD(nJD, out nYear, out nMonth, out nDay);
        JS2HMS(dJS, out nHour, out nMinute, out nSecond, out dMS);

        DateTime dt = new DateTime(nYear, nMonth, nDay, nHour, nMinute, nSecond);
        dt.AddMilliseconds(dMS);
                
        return dt;
        
    }
    /// <summary>
    /// 计算格林尼治恒星时(1950.0)
    /// 单位 rad
    /// </summary>
    /// <param name="nJd">积日（天）</param>
    /// <param name="dwJs">积秒（0.1ms）</param>
    /// <param name="isBJ">输入的时间是否为北京时</param>
    /// <returns></returns>
    static public double StarTime(int nJd, uint dwJs, bool isBJ)
    {
        ////////////////////////////////////////////////////////////////////
        // 将时间化成秒为单位
        double dJs = dwJs / 10000.0;
        if (isBJ)
        {   //如果是输入的为北京时，转为世界时
            dJs = dJs - 8 * 3600;
        }

        while (dJs > 86400)
        {
            dJs -= 86400.0;
            nJd++;
        }

        ////////////////////////////////////////////////////////////////////
        // 计算恒星时,保证恒星时小于360
        double dSt = 99.0899274 + 0.98561228626 * nJd + 360.98561229 * dJs / 3600.0 / 24.0;
        dSt = (dSt % 360.0) * Math.PI / 180.0;

        return dSt;
    }
    /// <summary>
    /// 计算本地格林尼治恒星时(1950.0)
    /// 单位 rad
    /// </summary>
    /// <param name="nJd"></param>
    /// <param name="dwJs"></param>
    /// <param name="lonL"> 本地经度 rad</param>
    /// <returns></returns>
    static public double StarLocalTime(int nJd, uint dwJs, double lonL)
    {
        // 计算地方恒星时, 并转换成弧度单位
        // 将时间化成秒为单位
        double dJs = dwJs / 10000.0;
        while (dJs > 86400)
        {
            dJs -= 86400.0;
            nJd++;
        }    
        // 计算恒星时,保证恒星时小于360
        double dLocalStarTime = 99.0899274 + 0.98561228626 * nJd + 360.98561229 * (dJs / 3600.0 - 8.0) / 24.0 
                                + lonL * 180 / Math.PI;
        double dSci = (dLocalStarTime % 360) * Math.PI / 180.0;
        return dSci;
    }
#endregion

#region 坐标变换函数
    /*
    //参心大地坐标系WGS84-->参心直角坐标系  公式（1）
    static public double[] lla2ecef(double[] BLH)
    {	//Convert Earth-centered Earth-fixed (ECEF) coordinates to geodetic coordinates
        //(B:latitude 纬度deg, L:longitude 经度deg， H:altitude 对椭球高程)

        double a = 6378137.0;			//长半轴 Semimajor axis
        double f = 1 / 298.257223563;		// 椭球偏率 Flattening f = 1 - Math.Sqrt(1 - e2);    
        double e2 = 1 - Math.Pow(1 - f, 2);	// 第一偏心率平方 Square of first eccentricity

        double B = BLH[0];
        double L = BLH[1];
        double H = BLH[2];
        double[] XYZ = { 0, 0, 0 };

        double phi, sinphi, lambda;
        phi = B * Math.PI / 180;
        lambda = L * Math.PI / 180;

        sinphi = Math.Sin(phi);
        double cosphi = Math.Cos(phi);
        double N = a / Math.Sqrt(1 - e2 * Math.Pow(sinphi, 2));

        XYZ[0] = (N + H) * cosphi * Math.Cos(lambda);
        XYZ[1] = (N + H) * cosphi * Math.Sin(lambda);
        XYZ[2] = (N * (1 - e2) + H) * sinphi;

        return XYZ;
    }

    //参心直角坐标系-->参心大地坐标系WGS84  公式（2）        
    static public double[] ecef2lla(double[] XYZ)
    {	//Convert Earth-centered Earth-fixed (ECEF) coordinates to geodetic coordinates
        //(B:latitude 纬度deg, L:longitude 经度deg， H:altitude 对椭球高程)

        double a = 6378137.0;			//长半轴 Semimajor axis
        double f = 1 / 298.257223563;		// 椭球偏率 Flattening f = 1 - Math.Sqrt(1 - e2);    
        double e2 = 1 - Math.Pow(1 - f, 2);	// 第一偏心率平方 Square of first eccentricity
        double ep2 = e2 / (1 - e2);     // 第一偏心率平方 Square of second eccentricity	
        double b = a * (1 - f);         // 短半轴 Semiminor axis

        double x = XYZ[0];
        double y = XYZ[1];
        double z = XYZ[2];
        double[] BLH = { 0, 0, 0 };

        // 经度 Longitude [-pi, pi]
        double lambda = Math.Atan2(y, x);

        // 点到Z轴的距离 Distance from Z-axis
        double rho = Math.Sqrt(x * x + y * y);

        // Bowring's formula for initial parametric (beta) and geodetic (phi) latitudes
        double beta = Math.Atan2(z, (1 - f) * rho);
        double phi = Math.Atan2(z + b * ep2 * Math.Pow(Math.Sin(beta), 3), rho - a * e2 * Math.Pow(Math.Cos(beta), 3));

        // Fixed-point iteration with Bowring's formula
        // (typically converges within two or three iterations)
        double betaNew = Math.Atan2((1 - f) * Math.Sin(phi), Math.Cos(phi));
        int count = 0;
        while ((beta != betaNew) && count < 5)
        {

            beta = betaNew;
            phi = Math.Atan2(z + b * ep2 * Math.Pow(Math.Sin(beta), 3), rho - a * e2 * Math.Pow(Math.Cos(beta), 3));
            betaNew = Math.Atan2((1 - f) * Math.Sin(phi), Math.Cos(phi));
            count = count + 1;
        }

        // Calculate ellipsoidal height from the final value for latitude
        double sinphi = Math.Sin(phi);
        double N = a / Math.Sqrt(1 - e2 * Math.Pow(sinphi, 2));
        double h = rho * Math.Cos(phi) + (z + e2 * N * sinphi) * sinphi - N;

        BLH[0] = phi * 180 / Math.PI;
        BLH[1] = lambda * 180 / Math.PI;
        BLH[2] = h;

        return BLH;
    }
    //站心直角坐标系xyz-->参心大地直角坐标系XYZ  公式（3）
    static public double[] SCxyz2XYZ(double[] SCxyz, double[] BLHs)
    {   // BLHs:站心经纬高，以度和米为单位; SCxyz:目标在站心坐标系下的空间坐标，以m为单位
        // 输出XYZ:目标地心系下的坐标,以m为单位
        double B0, L0;
        double[] XYZs = { 0, 0, 0 };
        double[] XYZ = { 0, 0, 0 };

        XYZs = lla2ecef(BLHs);  //调用公式（1），站心经纬高-->站心XYZs

        B0 = BLHs[0] * Math.PI / 180;
        L0 = BLHs[1] * Math.PI / 180;

        XYZ[0] = XYZs[0] - Math.Sin(B0) * Math.Cos(L0) * SCxyz[0] - Math.Sin(L0) * SCxyz[1]
                 + Math.Cos(B0) * Math.Cos(L0) * SCxyz[2];
        XYZ[1] = XYZs[1] - Math.Sin(B0) * Math.Sin(L0) * SCxyz[0] + Math.Cos(L0) * SCxyz[1]
                + Math.Cos(B0) * Math.Sin(L0) * SCxyz[2];
        XYZ[2] = XYZs[2] + Math.Cos(B0) * SCxyz[0] + Math.Sin(B0) * SCxyz[2];

        return XYZ;
    }

    //参心大地直角坐标系XYZ-->站心直角坐标系xyz  公式（4）
    static public double[] XYZ2SCxyz(double[] XYZ, double[] BLHs)
    {   // XYZ:目标坐标,以m为单位;  BLHs:站心经纬高，以度和米为单位
        // SCxyz:目标在站心坐标系下的空间坐标，以m为单位
        double deltX, deltY, deltZ, B0, L0;
        double[] XYZs = { 0, 0, 0 };
        double[] SCxyz = { 0, 0, 0 };

        XYZs = lla2ecef(BLHs);  //调用公式（1），站心经纬高-->站心XYZs

        B0 = BLHs[0] * Math.PI / 180;
        L0 = BLHs[1] * Math.PI / 180;

        deltX = XYZ[0] - XYZs[0];
        deltY = XYZ[1] - XYZs[1];
        deltZ = XYZ[2] - XYZs[2];

        SCxyz[0] = -Math.Sin(B0) * Math.Cos(L0) * deltX - Math.Sin(B0) * Math.Sin(L0) * deltY
                    + Math.Cos(B0) * deltZ;
        SCxyz[1] = -Math.Sin(L0) * deltX + Math.Cos(L0) * deltY;
        SCxyz[2] = Math.Cos(B0) * Math.Cos(L0) * deltX + Math.Cos(B0) * Math.Sin(L0) * deltY
                    + Math.Sin(B0) * deltZ;

        return SCxyz;
    }
    //站心极坐标系RAE-->站心直角坐标系xyz   公式（5）
    static public double[] RAE2SCxyz(double[] RAE)
    {   //RAE:站心极坐标系 R:以米为单位，A:方位，正北为0度，向东为正[0, 360]； E:俯仰，水平为0，向上为正[-90, +90]
        //输出SCxyz: 站心直角坐标系xyz，以米为单位 
        double[] SCxyz = { 0, 0, 0 };
        double A = RAE[1] * Math.PI / 180;
        double E = RAE[2] * Math.PI / 180;

        SCxyz[0] = RAE[0] * Math.Cos(E) * Math.Cos(A);
        SCxyz[1] = RAE[0] * Math.Cos(E) * Math.Sin(A);
        SCxyz[2] = RAE[0] * Math.Sin(E);

        return SCxyz;
    }
    /// <summary>
    /// 站心直角坐标系xyz-->站心极坐标系RAE   公式（6）
    /// </summary>
    /// <param name="SCxyz"></param>
    /// <returns></returns>
    static public double[] SCxyz2RAE(double[] SCxyz)

    {   // xyz:以m为单位, R:以m为单位，A：[0, 360], E:[-90, +90]
        double x = SCxyz[0];
        double y = SCxyz[1];
        double z = SCxyz[2];
        double[] RAE = { 0, 0, 0 };
        double temA;

        RAE[0] = Math.Sqrt(x * x + y * y + z * z);
        RAE[2] = Math.Asin(y / RAE[0]) * 180 / Math.PI;
        if (x * x + z * z == 0)
        {
            RAE[1] = RAE[2];
        } 
        else
        {
            temA = Math.Asin(z / Math.Sqrt(x * x + z * z));
            if (x >=0 )
            {
                if (z >= 0)                
                    RAE[1] = temA * 180 / Math.PI;                 
                else
                    RAE[1] = temA * 180 / Math.PI + 360;                
            } 
            else
            {
                RAE[1] = 180 - temA * 180 / Math.PI;
            }
        }
        

   //     temA = Math.Atan2(y, x) * 180 / Math.PI;  //---
//         temA = Math.Atan2(y, x) * 180 / Math.PI;
//         if (temA < 0)
//             RAE[1] = 360 + temA;
//         else
//             RAE[1] = temA;

   //     RAE[2] = Math.Atan2(z, Math.Sqrt(x * x + y * y)) * 180 / Math.PI; //--      
        return RAE;
    }

    //参心大地直角坐标系XYZ-->站心极坐标系RAE  结合公式（1）、公式（4）和公式（6）
    static public double[] XYZ2RAE(double[] XYZ, double[] BLHs)
    {   // XYZ:目标坐标,以m为单位;  BLHs:站心经纬高，以度和米为单位
        // R:以m为单位，A：[0, 2*pi], E:[-pi/2, +pi/2]
        double[] SCxyz = { 0, 0, 0 };
        double[] RAE = { 0, 0, 0 };

        SCxyz = XYZ2SCxyz(XYZ, BLHs);
        RAE = SCxyz2RAE(SCxyz);

        return RAE;
    }
    //站心极坐标系RAE-->参心大地坐标系WGS84 结合公式（5）、（3）和公式（2）
    static public double[] RAE2BLH(double[] RAE, double[] BLHs)
    {
        double[] SCxyz = { 0, 0, 0 };
        double[] XYZ = { 0, 0, 0 };
        double[] BLH = { 0, 0, 0 };

        SCxyz = RAE2SCxyz(RAE);
        XYZ = SCxyz2XYZ(SCxyz, BLHs);
        BLH = ecef2lla(XYZ);

        return BLH;
    }
    // 轨道坐标系Exyz-->参大地直角坐标系Gxyz
    static public void Exyz2Gxyz(double dSt, double[] Exyz, double[] Ev, out double[] Gxyz, out double[] Gv)
    {
        //输入 dSt:格林尼治恒星时（rad）
        // Exyz:轨道坐标系下的目标坐标
        // Ev:轨道坐标系下的目标速度
        // 输出 Gxyz:大地直角坐标下的坐标
        // Gv:大地直角坐标系下的速度
        double SRate = 0.000072921151467;   // 恒星时变率(rad/s)
        Gxyz = new double[3];   //大地直角坐标系下的位置
        Gv = new double[3];   //大地直角坐标系下的速度

        Gxyz[0] = Math.Cos(dSt) * Exyz[0] + Math.Sin(dSt) * Exyz[1];
        Gxyz[1] = -Math.Sin(dSt) * Exyz[0] + Math.Cos(dSt) * Exyz[1];
        Gxyz[2] = Exyz[2];

        Gv[0] = -SRate * Math.Sin(dSt) * Ev[0] + SRate * Math.Cos(dSt) * Ev[1];
        Gv[1] = SRate * Math.Cos(dSt) * Ev[0] - SRate * Math.Sin(dSt) * Ev[1];
        Gv[2] = 0;
    }
*/
    #endregion
    
#region 新的坐标变换
    /// <summary>
    /// 站心直角坐标系xyz-->站心极坐标系RAE   公式（11）
    /// </summary>
    /// <param name="Rxyz">位置和速度向量 1*6</param>
    /// <returns>RAE和变化率向量 1*6</returns>
    static public double[] staRxyz2RAE(double[] Rxyz)
    {   // 距离或位置的单位:m, 角度单位:度，A：[0, 360], E:[-90, +90]
        // 变化率的单位: m/s 或 度/s
        double x = Rxyz[0];
        double y = Rxyz[1];
        double z = Rxyz[2];
        double dx = Rxyz[3];
        double dy = Rxyz[4];
        double dz = Rxyz[5];

        double[] RAE = new double[6];
        double temA;

        //位置
        RAE[0] = Math.Sqrt(x * x + y * y + z * z);  // R
        RAE[2] = Math.Asin(y / RAE[0]) * 180 / Math.PI; // E
        if (x * x + z * z == 0)
        {
            RAE[1] = RAE[2];
        }
        else
        {
            temA = Math.Asin(z / Math.Sqrt(x * x + z * z));
            if (x >= 0)
            {
                if (z >= 0)
                    RAE[1] = temA * 180 / Math.PI;  // A
                else
                    RAE[1] = temA * 180 / Math.PI + 360;
            }
            else
            {
                RAE[1] = 180 - temA * 180 / Math.PI;
            }
        }

        // 变化率
        RAE[3] = (x * dx + y * dy + z * dz) / RAE[0];                   // dR
        RAE[4] = (x * dz - z * dx) / (x * x + z * z) * 180 /Math.PI;    // dA
        RAE[5] = (-x * y * dx + (x * x + z * z) * dy - y * z * dz) / 
                (RAE[0] * RAE[0] * Math.Sqrt(x * x + z * z)) * 180 / Math.PI;   //dE

        return RAE;
    }
    
    /// <summary>
    /// 站心极坐标系RAE-->站心直角坐标系xyz   公式（9, 10）
    /// </summary>
    /// <param name="RAE">RAE和变化率向量 1*6</param>
    /// <returns>位置和速度向量 1*6</returns>
    static public double[] RAE2staRxyz(double[] RAE)
    {   //RAE:站心极坐标系 R:以米为单位，A:方位，正北为0度，向东为正[0, 360]； E:俯仰，水平为0，向上为正[-90, +90]
        //输出SCxyz: 站心直角坐标系xyz，以米为单位 
        double[] Rxyz = new double[6];
        double R = RAE[0];
        double A = RAE[1] * Math.PI / 180;
        double E = RAE[2] * Math.PI / 180;
        double dR = RAE[3];
        double dA = RAE[4] * Math.PI / 180;
        double dE = RAE[5] * Math.PI / 180;
        
        Rxyz[0] = R * Math.Cos(E) * Math.Cos(A);
        Rxyz[1] = R * Math.Sin(E);
        Rxyz[2] = R * Math.Cos(E) * Math.Sin(A);

        Rxyz[3] = dR * Math.Cos(E) * Math.Cos(A) - Rxyz[1] * dE * Math.Cos(A) - Rxyz[2] * dA;   // dx
        Rxyz[4] = dR * Math.Sin(E) + R * dE * Math.Cos(E);                                      // dy
        Rxyz[5] = dR * Math.Cos(E) * Math.Sin(A) - Rxyz[1] * dE * Math.Sin(A) + Rxyz[0] * dA;   // dz

        return Rxyz;
    }
        
    /// <summary>
    /// 纬经高 --> 地心直角坐标 （基于WGS-84参心坐标系） 公式（A）
    /// </summary>
    /// <param name="BLH"></param>
    /// <returns></returns>
    static public double[] BLH2Gxyz(double[] BLH)
    {	//Convert Earth-centered Earth-fixed (ECEF) coordinates to geodetic coordinates
        //(B:latitude 纬度deg, L:longitude 经度deg， H:altitude 对椭球高程)

        double a = 6378137.0;			//长半轴 Semimajor axis
        double f = 1 / 298.257223563;		// 椭球偏率 Flattening f = 1 - Math.Sqrt(1 - e2);    
        double e2 = 1 - Math.Pow(1 - f, 2);	// 第一偏心率平方 Square of first eccentricity

        double B = BLH[0];
        double L = BLH[1];
        double H = BLH[2];
        double[] XYZ = { 0, 0, 0 };

        double phi, sinphi, lambda;
        phi = B * Math.PI / 180;
        lambda = L * Math.PI / 180;

        sinphi = Math.Sin(phi);
        double cosphi = Math.Cos(phi);
        double N = a / Math.Sqrt(1 - e2 * Math.Pow(sinphi, 2));

        XYZ[0] = (N + H) * cosphi * Math.Cos(lambda);
        XYZ[1] = (N + H) * cosphi * Math.Sin(lambda);
        XYZ[2] = (N * (1 - e2) + H) * sinphi;

        return XYZ;
    }
       
    /// <summary>
    /// 地心直角坐标 --> 纬经高 （基于WGS-84参心坐标系） 公式（B）
    /// </summary>
    /// <param name="XYZ"></param>
    /// <returns></returns>   
    static public double[] Gxyz2BLH(double[] XYZ)
    {	//Convert Earth-centered Earth-fixed (ECEF) coordinates to geodetic coordinates
        //(B:latitude 纬度deg, L:longitude 经度deg， H:altitude 对椭球高程)

        double a = 6378137.0;			//长半轴 Semimajor axis
        double f = 1 / 298.257223563;		// 椭球偏率 Flattening f = 1 - Math.Sqrt(1 - e2);    
        double e2 = 1 - Math.Pow(1 - f, 2);	// 第一偏心率平方 Square of first eccentricity
        double ep2 = e2 / (1 - e2);     // 第一偏心率平方 Square of second eccentricity	
        double b = a * (1 - f);         // 短半轴 Semiminor axis

        double x = XYZ[0];
        double y = XYZ[1];
        double z = XYZ[2];
        double[] BLH = { 0, 0, 0 };

        // 经度 Longitude [-pi, pi]
        double lambda = Math.Atan2(y, x);

        // 点到Z轴的距离 Distance from Z-axis
        double rho = Math.Sqrt(x * x + y * y);

        // Bowring's formula for initial parametric (beta) and geodetic (phi) latitudes
        double beta = Math.Atan2(z, (1 - f) * rho);
        double phi = Math.Atan2(z + b * ep2 * Math.Pow(Math.Sin(beta), 3), rho - a * e2 * Math.Pow(Math.Cos(beta), 3));

        // Fixed-point iteration with Bowring's formula
        // (typically converges within two or three iterations)
        double betaNew = Math.Atan2((1 - f) * Math.Sin(phi), Math.Cos(phi));
        int count = 0;
        while ((beta != betaNew) && count < 5)
        {

            beta = betaNew;
            phi = Math.Atan2(z + b * ep2 * Math.Pow(Math.Sin(beta), 3), rho - a * e2 * Math.Pow(Math.Cos(beta), 3));
            betaNew = Math.Atan2((1 - f) * Math.Sin(phi), Math.Cos(phi));
            count = count + 1;
        }

        // Calculate ellipsoidal height from the final value for latitude
        double sinphi = Math.Sin(phi);
        double N = a / Math.Sqrt(1 - e2 * Math.Pow(sinphi, 2));
        double h = rho * Math.Cos(phi) + (z + e2 * N * sinphi) * sinphi - N;

        BLH[0] = phi * 180 / Math.PI;
        BLH[1] = lambda * 180 / Math.PI;
        BLH[2] = h;

        return BLH;
    }
    /// <summary>
    /// 测站直角系R-->地心直角系G   公式（5，6）
    /// </summary>
    /// <param name="Rxyz"></param>
    /// <param name="staBLH"></param>
    /// <returns></returns>
    static public double[] staRxyz2Gxyz(double[] Rxyz, double[] staBLH)
    {
        double[] Gxyz = new double[6];
        double[] Grxyz = BLH2Gxyz(staBLH);  //测站的地心系直角坐标
        double Rx, Ry, Rz, dRx, dRy, dRz;   //目标的测站直角坐标
        double B, L;
        B = staBLH[0] * Math.PI / 180;
        L = staBLH[1] * Math.PI / 180;

        Rx = Rxyz[0];   //位置
        Ry = Rxyz[1];
        Rz = Rxyz[2];
        dRx = Rxyz[3];  //速度
        dRy = Rxyz[4];
        dRz = Rxyz[5];

        Gxyz[0] = (Grxyz[0]) - Math.Cos(L) * Math.Sin(B) * Rx + Math.Cos(L) * Math.Cos(B) * Ry - Math.Sin(L) * Rz;
        Gxyz[1] = (Grxyz[1]) - Math.Sin(L) * Math.Sin(B) * (Rx) + Math.Sin(L) * Math.Cos(B) * (Ry) + Math.Cos(L) * (Rz);
        Gxyz[2] = (Grxyz[2]) + Math.Cos(B) * (Rx) + Math.Sin(B) * (Ry);

        Gxyz[3] = -Math.Cos(L) * Math.Sin(B) * dRx + Math.Cos(L) * Math.Cos(B) * dRy - Math.Sin(L) * dRz;
        Gxyz[4] = -Math.Sin(L) * Math.Sin(B) * (dRx) + Math.Sin(L) * Math.Cos(B) * (dRy) + Math.Cos(L) * (dRz);
        Gxyz[5] = Math.Cos(B) * (dRx) + Math.Sin(B) * (dRy);

        return Gxyz;
    }
    //
    /// <summary>
    /// 地心直角坐标系G-->测站直角坐标系R  公式（7, 8）
    /// </summary>
    /// <param name="Gxyz"></param>
    /// <param name="staBLH"></param>
    /// <returns></returns>
    static public double[] Gxyz2staRxyz(double[] Gxyz, double[] staBLH)
    {   // XYZ:目标坐标,以m为单位;  BLHs:站心经纬高，以度和米为单位
        // SCxyz:目标在站心坐标系下的空间坐标，以m为单位
        double deltX, deltY, deltZ, B0, L0;
        double[] Rxyz = new double[6];
        double[] Grxyz = BLH2Gxyz(staBLH);  //测站的地心系直角坐标       

        B0 = staBLH[0] * Math.PI / 180;
        L0 = staBLH[1] * Math.PI / 180;

        deltX = Gxyz[0] - Grxyz[0];
        deltY = Gxyz[1] - Grxyz[1];
        deltZ = Gxyz[2] - Grxyz[2];

        Rxyz[0] = -Math.Sin(B0) * Math.Cos(L0) * deltX - Math.Sin(B0) * Math.Sin(L0) * deltY
                    + Math.Cos(B0) * deltZ;
        Rxyz[1] = Math.Cos(B0) * Math.Cos(L0) * deltX + Math.Cos(B0) * Math.Sin(L0) * deltY
                    + Math.Sin(B0) * deltZ;
        Rxyz[2] = -Math.Sin(L0) * deltX + Math.Cos(L0) * deltY;
        Rxyz[3] = -Math.Sin(B0) * Math.Cos(L0) * Gxyz[3] - Math.Sin(B0) * Math.Sin(L0) * Gxyz[4]
                    + Math.Cos(B0) * Gxyz[5];
        Rxyz[4] = Math.Cos(B0) * Math.Cos(L0) * Gxyz[3] + Math.Cos(B0) * Math.Sin(L0) * Gxyz[4]
                    + Math.Sin(B0) * Gxyz[5];
        Rxyz[5] = -Math.Sin(L0) * Gxyz[3] + Math.Cos(L0) * Gxyz[4];

        return Rxyz;
    }
    /// <summary>
    /// 卫星大地直角坐标-->可见卫星的边界坐标(以大地直角坐标输出)
    /// </summary>
    /// <param name="Gxyz"></param>
    /// <returns></returns>
    static public double[,] Gxyz2Gedge(double[] Gxyz)
    {
        double[,] Gedge = new double[360, 3];

        double[] satBLH = Gxyz2BLH(Gxyz);   //卫星纬经高
        satBLH[2] = 0;  //星下点纬经高
        double[] Rg = BLH2Gxyz(satBLH); //星下点大地直角坐标
        double Rs = Math.Sqrt(Gxyz[0] * Gxyz[0] + Gxyz[1] * Gxyz[1] + Gxyz[2] * Gxyz[2]);
        //Rs 卫星与地心距离
        double Ra = dEarthAdius * Math.Sqrt(1 - Math.Pow((dEarthAdius / Rs), 2));
        double Ya = dEarthAdius * (1 - dEarthAdius / Rs);
        double B = satBLH[0] * Math.PI / 180;
        double L = satBLH[1] * Math.PI / 180;
        for (int Qdeg = 0; Qdeg < 360; Qdeg++)
        {
            double Qred = (double)Qdeg * Math.PI / 180; //边界点方位
            double[] Rxyz = { 0, 0, 0 };
            Rxyz[0] = Ra * Math.Cos(Qred);
            Rxyz[2] = Ra * Math.Sin(Qred);
            Rxyz[1] = -Ya;

            Gedge[Qdeg, 0] = Math.Sin(L) * (-Rxyz[2]) + Math.Cos(L) * Math.Cos(B) * Rxyz[1]
                                - Math.Cos(L) * Math.Sin(B) * Rxyz[0] + Rg[0];
            Gedge[Qdeg, 1] = -Math.Cos(L) * (-Rxyz[2]) + Math.Sin(L) * Math.Cos(B) * Rxyz[1]
                                - Math.Sin(L) * Math.Sin(B) * Rxyz[0] + Rg[1];
            Gedge[Qdeg, 2] = Math.Sin(B) * Rxyz[1] + Math.Cos(B) * Rxyz[0] + Rg[2];
        }

        return Gedge;
    }
#endregion

#region 信号计算相关函数
    /// <summary>
    /// 计算双向多普勒频移,并转换为KHz
    /// </summary>
    /// <param name="dR"></param>
    /// <param name="freUp"></param>
    /// <param name="freTurnN"></param>
    /// <returns></returns>
    public static double GetFd(double dR, double freUp, double freTurnN)
    {
        double freDoppler = -2 * dR * freTurnN * freUp / (dR + OrbitForcast.dC) * 1000.0;
        return freDoppler;
    }
    /// <summary>
    /// 计算地面天线接收卫星信号的功率(dBw) 以10m天线为例
    /// </summary>
    /// <param name="EIRP">星上功率(dBw),参考-12</param>
    /// <param name="freDn">下行信号频率(MHz)</param>
    /// <param name="R">星地距离(km)</param>
    /// <param name="Gr">地面天线接收增益(dBm)</param>
    /// <returns></returns>
    public static double GetPowerRcv(double EIRP, double freDn, double R)
    {
        double Lsp = 32.44 + 20.0 * Math.Log10(freDn) + 20.0 * Math.Log10(R);
        double PwrRcv = EIRP - Lsp + 44.7 + 20 * Math.Log10( freDn /2200) + 30;
        return PwrRcv;
    }
    /// <summary>
    /// 计算地面接收S/Φ
    /// </summary>
    /// <param name="EIRP">星上功率(dBw)，</param>
    /// <param name="freDn">下行信号频率(MHz)</param>
    /// <param name="R">星地距离(km)</param>
    /// <param name="Gq">地面天线接收增益与噪底的比(dB)，参考207</param>
    /// <returns></returns>
    public static double GetSprRecv(double EIRP, double freDn, double R)
    {
        double sqr;
        double Gq = 207;
        double Lsp = 32.44 + 20.0 * Math.Log10(freDn) + 20.0 * Math.Log10(R);
        sqr = EIRP - Lsp + Gq + 45;
        return sqr;
    }
#endregion
    
}
