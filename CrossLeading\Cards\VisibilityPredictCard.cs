﻿using MyListBox;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Windows.Forms;
using System.Globalization;
using System.Linq;
using CrossLeading.Net;
using CrossLeading.Models;
using CrossLeading.Forms;

namespace CrossLeading.Cards
{
    /// <summary>
    /// 卫星轨道数据结构
    /// </summary>
    public struct SatelliteOrbitData
    {
        public DateTime Time;
        public double X, Y, Z;      // ECF坐标 (m)
        public double Vx, Vy, Vz;  // ECF速度 (m/s)
    }

    /// <summary>
    /// 测站可见性数据结构
    /// </summary>
    public struct StationVisibilityData
    {
        public DateTime Time;
        public double Azimuth;      // 方位角 (度)
        public double Elevation;    // 俯仰角 (度)
        public double Range;        // 距离 (km)
        public bool IsVisible;      // 是否可见
    }

    /// <summary>
    /// 共视分析结果数据结构
    /// </summary>
    public struct CoVisibilityResult
    {
        public DateTime Time;
        public bool IsCoVisible;    // 是否共视
        public StationVisibilityData StationA;
        public StationVisibilityData StationB;
    }

    public partial class VisibilityPredictCard : UserControl
    {
        public VisibilityPredictCard()
        {
            InitializeComponent();
            InitDevConfList();
        }

        /// <summary>
        /// 初始化共视预报结果列表视图
        /// </summary>
        private void InitDevConfList()
        {
            lvPredictResult.View = View.Details;
            lvPredictResult.FullRowSelect = true;
            lvPredictResult.GridLines = true;

            // 清除现有列
            lvPredictResult.Columns.Clear();

            // 添加列标题
            lvPredictResult.Columns.Add("时刻", 200, HorizontalAlignment.Left);
            lvPredictResult.Columns.Add("是否共视", 90, HorizontalAlignment.Center);
            lvPredictResult.Columns.Add("A站方位(°)", 125, HorizontalAlignment.Right);
            lvPredictResult.Columns.Add("A站俯仰(°)", 125, HorizontalAlignment.Right);
            lvPredictResult.Columns.Add("A站距离(km)", 125, HorizontalAlignment.Right);
            lvPredictResult.Columns.Add("B站方位(°)", 125, HorizontalAlignment.Right);
            lvPredictResult.Columns.Add("B站俯仰(°)", 125, HorizontalAlignment.Right);
            lvPredictResult.Columns.Add("B站距离(km)", 125, HorizontalAlignment.Right);
            lvPredictResult.Columns.Add("备注", 100, HorizontalAlignment.Left);
        }

        /// <summary>
        /// 根据devConfDict设备配置字典初始化源设备和被引导设备的下拉列表项
        /// </summary>
        public void InitCbItems()
        {
            try
            {
                Form1.LoadDevConf();
            }
            catch (Exception e)
            {
                MessageBox.Show("请检查配置文件格式是否正确！");
                // throw new Exception("请检查配置文件格式是否正确！");
            }

            cbSrcDev.Items.Clear();
            cbTarDev.Items.Clear();
            foreach (KeyValuePair<string, DevConf> kv in Form1.devConfDict)
            {
                Console.WriteLine(kv.Key, kv.Value);
                string devName = kv.Key;
                DevConf devConf = kv.Value;
                //if (devName.StartsWith("QD") && devConf.isQD)
                //{
                //    InitQDBeamItems(devName);
                //    continue;
                //}
                cbSrcDev.Items.Add(kv.Value.devName);
                cbTarDev.Items.Add(kv.Value.devName);
            }
            cbSrcDev.Text = cbSrcDev.Items[0].ToString();
            cbTarDev.Text = cbTarDev.Items[0].ToString();
        }

        ///// <summary>
        ///// 初始化下拉列表中的QD波束配置
        ///// </summary>
        //public void InitQDBeamItems(string devName)
        //{
        //    ListView uacLv = Form1.deviceConfigCard.uacConfList;
        //    foreach (ListViewItem item in uacLv.Items)
        //    {
        //        if (item.SubItems[0].Text == devName)
        //        {
        //            cbSrcDev.Items.Add(item.SubItems[1].Text); // 索引0:设备名称，1:UAC名称，2:UAC地址
        //            cbTarDev.Items.Add(item.SubItems[1].Text); // 索引0:设备名称，1:UAC名称，2:UAC地址
        //        }
        //    }
        //}

        private void cbSrcDev_SelectedIndexChanged(object sender, EventArgs e)
        {

        }

        private void VisibilityPredictCard_VisibleChanged(object sender, EventArgs e)
        {
            if (Visible)
                InitCbItems();
        }

        // 从 orb_eph_orbit.ifm 读取历元时刻
        public static DateTime ReadEpochTime(string filePath)
        {
            if (!File.Exists(filePath))
                throw new FileNotFoundException($"输入文件不存在: {filePath}");

            string content = File.ReadAllText(filePath).Trim();
            string[] parts = content.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);

            if (parts.Length < 6)
                throw new FormatException("输入文件格式不正确，至少需要6个数字");

            // 解析前6个数字作为日期时间
            int year = int.Parse(parts[0]);
            int month = int.Parse(parts[1]);
            int day = int.Parse(parts[2]);
            int hour = int.Parse(parts[3]);
            int minute = int.Parse(parts[4]);
            int second = int.Parse(parts[5]);

            return new DateTime(year, month, day, hour, minute, second);
        }

        /// <summary>
        /// 写入轨道文件o
        /// </summary>
        /// <param name="filePath"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="interval"></param>
        public static void WriteOutputFile(string filePath, DateTime startTime, DateTime endTime, double interval)
        {
            // 创建目录（如果不存在）
            Directory.CreateDirectory(Path.GetDirectoryName(filePath));

            // 格式化时间输出，确保两位数显示
            string startStr = $"{startTime.Year:D4} {startTime.Month:D2} {startTime.Day:D2} " +
                             $"{startTime.Hour:D2} {startTime.Minute:D2} {startTime.Second:D2}";

            string endStr = $"{endTime.Year:D4} {endTime.Month:D2} {endTime.Day:D2} " +
                           $"{endTime.Hour:D2} {endTime.Minute:D2} {endTime.Second:D2}";

            // 构建文件内容
            string fileContent = $"{startStr}\r\n" +  // 起始时间行
                                $"{endStr}\r\n" +    // 结束时间行
                                $"{interval}\r\n" +  // 间隔行
                                "0";                // 最后的单独0

            // 写入文件
            File.WriteAllText(filePath, fileContent);
        }


        /// <summary>
        /// 生成卫星参数配置文件 sat_parameter.ifm
        /// </summary>
        /// <param name="workingDir">工作目录</param>
        private void GenerateSatParameterFile(string workingDir)
        {
            string filePath = Path.Combine(workingDir, "sat_parameter.ifm");

            // 使用默认的卫星参数
            string[] lines = {
                "FORCE_SATID_MASS    1000",
                "FORCE_DRAG_AREA    10.0",
                "FORCE_DRAG_CD    2.2",
                "FORCE_RADPR_AREA    10.0",
                "FORCE_RADPR_K    1.3",
                "FORCE_F10D7_AVERA    150",
                "FORCE_F10D7_DAILY    150",
                "FORCE_KP    3"
            };

            File.WriteAllLines(filePath, lines);
        }

        /// <summary>
        /// 处理轨道文件，生成时间配置
        /// </summary>
        /// <param name="inputPath">输入文件路径</param>
        /// <param name="outputPath">输出文件路径</param>
        /// <param name="startTime">开始预报时间</param>
        /// <param name="spanMinutes">时间跨度（分钟）</param>
        /// <param name="timeStep">时间步长（秒）</param>
        public static void ProcessFiles(string inputPath, string outputPath, DateTime startTime, double spanMinutes, double timeStep)
        {
            try
            {
                DateTime epochTime = ReadEpochTime(inputPath);

                //if (epochTime <= DateTime.Now.AddDays(-14))
                //{
                //    throw new Exception("轨根数据老旧，早于当前时刻14天，请更新轨道根数");
                //}
                if (startTime < epochTime)
                {
                    throw new Exception("开始预报时间不能早于轨道文件的历元时间！");
                }

                // 1. 使用指定的开始预报时间
                var beginTime = startTime;

                // 2. 计算结束时间
                var endTime = beginTime.AddMinutes(spanMinutes);
                if (endTime >= epochTime.AddDays(21))
                {
                    throw new Exception("预报结束时间超过历元时间21天将导致计算时间过长，请重新设置参数！");
                }

                // 3. 写入输出文件 orb_eph_start.ifm
                WriteOutputFile(outputPath, beginTime, endTime, timeStep);

                // 4. 生成卫星参数文件
                //string workingDir = Path.GetDirectoryName(outputPath);
                //GenerateSatParameterFile(workingDir);
            }
            catch (Exception ex)
            {
                throw new Exception($"轨道文件处理失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 验证输入参数
        /// </summary>
        /// <returns>验证是否通过</returns>
        private bool ValidateInputParameters()
        {
            try
            {
                // 验证卫星ID
                if (string.IsNullOrWhiteSpace(cbSatID.Text))
                {
                    MessageBox.Show("请选择或输入卫星ID！", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

                // 验证仰角限制
                if (!double.TryParse(tbELLimit.Text, out double elLimit) || elLimit < 0 || elLimit > 90)
                {
                    MessageBox.Show("仰角限制必须是0-90度之间的数值！", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

                // 验证时间跨度
                if (!double.TryParse(tbTimeSpan.Text, out double timeSpan) || timeSpan <= 0)
                {
                    MessageBox.Show("时间跨度必须是大于0的数值！", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

                if (double.TryParse(tbTimeSpan.Text, out double timeSPan) && timeSpan > 24 * 60 * 7)
                {
                    MessageBox.Show("时间跨度超过7天（10080分钟），将导致计算时间过长，请重新设置参数！");
                    return false;
                }

                // 验证时间步长
                if (!double.TryParse(tbTimeStep.Text, out double timeStep) || timeStep <= 0)
                {
                    MessageBox.Show("预报间隔必须是大于0的数值！", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

                // 验证开始预报时间
                //DateTime startTime = dtpStartTime.Value;
                //DateTime currentTime = DateTime.Now;
                //if (startTime < currentTime.AddMinutes(-60)) // 允许最多1小时前的时间
                //{
                //    MessageBox.Show("开始预报时间不能早于当前时间1小时！", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                //    return false;
                //}

                // 验证测站选择
                if (string.IsNullOrWhiteSpace(cbSrcDev.Text) || string.IsNullOrWhiteSpace(cbTarDev.Text))
                {
                    MessageBox.Show("请选择A站和B站！", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

                if (cbSrcDev.Text == cbTarDev.Text)
                {
                    MessageBox.Show("A站和B站不能是同一个测站！", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

                // 验证测站配置是否存在
                if (!Form1.devConfDict.ContainsKey(cbSrcDev.Text))
                {
                    MessageBox.Show($"A站配置不存在: {cbSrcDev.Text}\n请在设备配置中添加该测站！", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

                if (!Form1.devConfDict.ContainsKey(cbTarDev.Text))
                {
                    MessageBox.Show($"B站配置不存在: {cbTarDev.Text}\n请在设备配置中添加该测站！", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"参数验证时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 读取轨道外推结果文件 eph_ecf.txt
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>卫星轨道数据列表</returns>
        public static List<SatelliteOrbitData> ReadOrbitData(string filePath)
        {
            var orbitData = new List<SatelliteOrbitData>();

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"轨道数据文件不存在: {filePath}");
            }

            string[] lines = File.ReadAllLines(filePath);
            foreach (string line in lines)
            {
                if (string.IsNullOrWhiteSpace(line)) continue;

                string[] parts = line.Trim().Split(new char[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length >= 12)
                {
                    try
                    {
                        var data = new SatelliteOrbitData
                        {
                            Time = new DateTime(
                                int.Parse(parts[0]),    // year
                                int.Parse(parts[1]),    // month
                                int.Parse(parts[2]),    // day
                                int.Parse(parts[3]),    // hour
                                int.Parse(parts[4]),    // minute
                                (int)double.Parse(parts[5])  // second
                            ),
                            X = double.Parse(parts[6]),   // ECF X (m)
                            Y = double.Parse(parts[7]),   // ECF Y (m)
                            Z = double.Parse(parts[8]),   // ECF Z (m)
                            Vx = double.Parse(parts[9]),  // ECF Vx (m/s)
                            Vy = double.Parse(parts[10]), // ECF Vy (m/s)
                            Vz = double.Parse(parts[11])  // ECF Vz (m/s)
                        };
                        orbitData.Add(data);
                    }
                    catch (Exception ex)
                    {
                        // 跳过解析失败的行
                        Console.WriteLine($"解析轨道数据行失败: {line}, 错误: {ex.Message}");
                    }
                }
            }

            return orbitData;
        }

        /// <summary>
        /// 计算单个测站的可见性数据
        /// </summary>
        /// <param name="orbitData">卫星轨道数据</param>
        /// <param name="station">测站配置</param>
        /// <param name="elevationLimit">仰角限制 (度)</param>
        /// <returns>测站可见性数据列表</returns>
        private List<StationVisibilityData> ComputeStationVisibility(
            List<SatelliteOrbitData> orbitData, DevConf station, double elevationLimit)
        {
            var visibilityData = new List<StationVisibilityData>();

            // 将测站坐标转换为弧度
            double stationLon = station.longi * SatelliteMathUtils.DEG_TO_RAD;
            double stationLat = station.lati * SatelliteMathUtils.DEG_TO_RAD;
            double stationHeight = station.height;

            // 计算测站坐标和转换矩阵
            var (stationECEF, transformMatrix) = SatelliteMathUtils.ComputeStationCoordinate(
                stationLon, stationLat, stationHeight);

            foreach (var orbit in orbitData)
            {
                // 卫星ECEF坐标
                double[] satECEF = { orbit.X, orbit.Y, orbit.Z };

                // 计算AER
                var (azimuth, elevation, range) = SatelliteMathUtils.ComputeSatAccessAER(
                    satECEF, stationECEF, transformMatrix);

                // 调试用：之前因为ComputeStationCoordinate函数返回的矩阵没有变成转置，导致计算错误，调试用的，现在已经可以正确计算
                //double[] XYZ6 = new double[] { orbit.X, orbit.Y, orbit.Z, orbit.Vx, orbit.Vy, orbit.Vz};
                //double[] staXYZ2 = OrbitForcast.Gxyz2staRxyz(XYZ6, new double[] { station.lati, station.longi, station.height});
                //double[] RAE2 = OrbitForcast.staRxyz2RAE(staXYZ2);
                //double azimuthDeg = RAE2[1];
                //double elevationDeg = RAE2[2];
                //double rangeKm = RAE2[0] / 1000.0; // 转换为公里


                // 转换为度
                double azimuthDeg = azimuth * SatelliteMathUtils.RAD_TO_DEG;
                double elevationDeg = elevation * SatelliteMathUtils.RAD_TO_DEG;
                double rangeKm = range / 1000.0; // 转换为公里

                var visibility = new StationVisibilityData
                {
                    Time = orbit.Time,
                    Azimuth = azimuthDeg,
                    Elevation = elevationDeg,
                    Range = rangeKm,
                    IsVisible = elevationDeg >= elevationLimit
                };

                visibilityData.Add(visibility);
            }

            return visibilityData;
        }

        private void SingleSatAccessAnalysis()
        {
            try
            {
                // 1. 验证输入参数
                if (!ValidateInputParameters())
                {
                    return;
                }

                // 2. 清空上次计算结果
                lvPredictResult.Items.Clear();
                InitDevConfList(); // 重新初始化列标题

                // 3. 读取输入参数
                string satID = cbSatID.Text.Trim();
                double elLimit = double.Parse(tbELLimit.Text);
                double timeSpanMin = double.Parse(tbTimeSpan.Text);
                double timeStep = double.Parse(tbTimeStep.Text);
                DateTime startTime = dtpStartTime.Value;

                // 4. 获取测站信息
                if (!Form1.devConfDict.ContainsKey(cbSrcDev.Text))
                {
                    MessageBox.Show($"A站配置不存在: {cbSrcDev.Text}\n请检查设备配置！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                if (!Form1.devConfDict.ContainsKey(cbTarDev.Text))
                {
                    MessageBox.Show($"B站配置不存在: {cbTarDev.Text}\n请检查设备配置！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                DevConf staA = Form1.devConfDict[cbSrcDev.Text];
                DevConf staB = Form1.devConfDict[cbTarDev.Text];

                // 5. 处理轨道文件读取及写入
                string orbInFilePath = Path.Combine(Application.StartupPath, "02轨道外推", "orb_eph_orbit.ifm");
                string orbOutFilePath = Path.Combine(Application.StartupPath, "02轨道外推", "orb_eph_start.ifm");
                string workingDir = Path.Combine(Application.StartupPath, "02轨道外推");

                // 检查必要文件是否存在
                if (!File.Exists(orbInFilePath))
                {
                    MessageBox.Show($"轨道历元文件不存在: {orbInFilePath}\n请先设置轨道参数！",
                        "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }



                ProcessFiles(orbInFilePath, orbOutFilePath, startTime, timeSpanMin, timeStep);

                // 6. 调用orb_eph.exe执行计算
                string orbExePath = Path.Combine(workingDir, "orb_eph.exe");

                if (!File.Exists(orbExePath))
                {
                    MessageBox.Show($"轨道外推程序不存在: {orbExePath}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 显示进度提示
                this.Cursor = Cursors.WaitCursor;
                Application.DoEvents();

                ProcessStartInfo startInfo = new ProcessStartInfo
                {
                    FileName = orbExePath,
                    WorkingDirectory = workingDir,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using (Process process = Process.Start(startInfo))
                {
                    process.WaitForExit(); // 30秒超时

                    if (!process.HasExited)
                    {
                        process.Kill();
                        MessageBox.Show("轨道外推计算超时！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }

                    if (process.ExitCode != 1 && process.ExitCode != 0)
                    {
                        string error = process.StandardError.ReadToEnd();
                        MessageBox.Show($"轨道外推计算失败，退出代码: {process.ExitCode}\n错误信息: {error}",
                            "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                }

                // 7. 读取轨道数据并计算共视条件
                string ephFilePath = Path.Combine(workingDir, "eph_ecf.txt");
                if (!File.Exists(ephFilePath))
                {
                    MessageBox.Show($"轨道数据文件不存在: {ephFilePath}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 读取轨道数据
                var orbitData = ReadOrbitData(ephFilePath);
                if (orbitData.Count == 0)
                {
                    MessageBox.Show("未读取到有效的轨道数据！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 计算两个测站的可见性
                var visibilityA = ComputeStationVisibility(orbitData, staA, elLimit);
                var visibilityB = ComputeStationVisibility(orbitData, staB, elLimit);

                // 分析共视条件并显示结果
                AnalyzeCoVisibilityAndDisplay(visibilityA, visibilityB, staA.devName, staB.devName);

                MessageBox.Show($"共视分析完成！\n" +
                              $"卫星: {satID}\n" +
                              $"A站: {staA.devName}\n" +
                              $"B站: {staB.devName}\n" +
                              $"共处理 {orbitData.Count} 个时刻的数据",
                    "完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"共视分析过程中出错：{ex.Message}\n\n详细信息：{ex.StackTrace}",
                    "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// 分析共视条件并显示结果
        /// </summary>
        /// <param name="visibilityA">测站A的可见性数据</param>
        /// <param name="visibilityB">测站B的可见性数据</param>
        /// <param name="stationAName">测站A名称</param>
        /// <param name="stationBName">测站B名称</param>
        private void AnalyzeCoVisibilityAndDisplay(
            List<StationVisibilityData> visibilityA,
            List<StationVisibilityData> visibilityB,
            string stationAName,
            string stationBName)
        {
            if (visibilityA.Count != visibilityB.Count)
            {
                MessageBox.Show("两个测站的数据点数量不匹配！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            var coVisibilityResults = new List<CoVisibilityResult>();

            // 分析每个时刻的共视条件
            for (int i = 0; i < visibilityA.Count; i++)
            {
                var dataA = visibilityA[i];
                var dataB = visibilityB[i];

                // 确保时间一致
                if (dataA.Time != dataB.Time)
                {
                    Console.WriteLine($"时间不匹配: A={dataA.Time}, B={dataB.Time}");
                    continue;
                }

                var result = new CoVisibilityResult
                {
                    Time = dataA.Time,
                    IsCoVisible = dataA.IsVisible && dataB.IsVisible,
                    StationA = dataA,
                    StationB = dataB
                };

                coVisibilityResults.Add(result);
            }

            // 显示结果到ListView
            DisplayCoVisibilityResults(coVisibilityResults, stationAName, stationBName);

            // 更新A站的距离预报数据到RangeForecastManager
            UpdateRangeForecastData(visibilityA, stationAName);
            UpdateRangeForecastData(visibilityB, stationBName);
        }

        /// <summary>
        /// 将共视分析结果显示到ListView控件
        /// </summary>
        /// <param name="results">共视分析结果</param>
        /// <param name="stationAName">测站A名称</param>
        /// <param name="stationBName">测站B名称</param>
        private void DisplayCoVisibilityResults(
            List<CoVisibilityResult> results,
            string stationAName,
            string stationBName)
        {
            lvPredictResult.BeginUpdate();

            try
            {
                // 统计共视时段
                int coVisibleCount = 0;
                var coVisiblePeriods = new List<(DateTime start, DateTime end)>();
                DateTime? currentPeriodStart = null;

                foreach (var result in results)
                {
                    if (result.IsCoVisible)
                    {
                        coVisibleCount++;
                        if (currentPeriodStart == null)
                        {
                            currentPeriodStart = result.Time;
                        }
                    }
                    else
                    {
                        if (currentPeriodStart != null)
                        {
                            coVisiblePeriods.Add((currentPeriodStart.Value, result.Time));
                            currentPeriodStart = null;
                        }
                    }

                    // 添加到ListView
                    var item = new ListViewItem(result.Time.ToString("yyyy-MM-dd HH:mm:ss"));
                    item.SubItems.Add(result.IsCoVisible ? "是" : "否");
                    item.SubItems.Add(result.StationA.Azimuth.ToString("F2"));
                    item.SubItems.Add(result.StationA.Elevation.ToString("F2"));
                    item.SubItems.Add(result.StationA.Range.ToString("F2"));
                    item.SubItems.Add(result.StationB.Azimuth.ToString("F2"));
                    item.SubItems.Add(result.StationB.Elevation.ToString("F2"));
                    item.SubItems.Add(result.StationB.Range.ToString("F2"));
                    item.SubItems.Add(""); // 预留列

                    // 根据共视状态设置颜色
                    if (result.IsCoVisible)
                    {
                        item.BackColor = System.Drawing.Color.LightGreen;
                    }
                    else if (result.StationA.IsVisible || result.StationB.IsVisible)
                    {
                        item.BackColor = System.Drawing.Color.LightYellow;
                    }

                    lvPredictResult.Items.Add(item);
                }

                // 处理最后一个共视时段
                if (currentPeriodStart != null && results.Count > 0)
                {
                    coVisiblePeriods.Add((currentPeriodStart.Value, results.Last().Time));
                }

                // 显示统计信息
                string summary = $"共视分析完成！\n" +
                               $"总时间点: {results.Count}\n" +
                               $"共视时间点: {coVisibleCount}\n" +
                               $"共视比例: {(coVisibleCount * 100.0 / results.Count):F1}%\n" +
                               $"共视时段数: {coVisiblePeriods.Count}";

                if (coVisiblePeriods.Count > 0)
                {
                    summary += "\n\n主要共视时段:";
                    for (int i = 0; i < Math.Min(5, coVisiblePeriods.Count); i++)
                    {
                        var period = coVisiblePeriods[i];
                        var duration = period.end - period.start;
                        summary += $"\n{i + 1}. {period.start:HH:mm:ss} - {period.end:HH:mm:ss} (时长: {duration.TotalMinutes:F1}分钟)";
                    }
                }

                // 可以选择是否显示详细统计信息
                // MessageBox.Show(summary, "共视分析统计", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            finally
            {
                lvPredictResult.EndUpdate();
            }
        }

        /// <summary>
        /// 更新测站的距离预报数据到RangeForecastManager
        /// </summary>
        /// <param name="visibilityData">测站可见性数据</param>
        /// <param name="stationName">测站名称</param>
        private void UpdateRangeForecastData(List<StationVisibilityData> visibilityData, string stationName)
        {
            try
            {
                var rangeForecastData = new List<RangeForecastData>();

                foreach (var data in visibilityData)
                {
                    rangeForecastData.Add(new RangeForecastData
                    {
                        Time = data.Time,
                        Range = data.Range // 距离已经是km单位
                    });
                }

                // 更新到RangeForecastManager
                RangeForecastManager.UpdateStationRangeData(stationName, rangeForecastData);

                Console.WriteLine($"已更新测站 {stationName} 的距离预报数据，共 {rangeForecastData.Count} 个数据点");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新距离预报数据失败: {ex.Message}");
            }
        }

        private void btCalc_Click(object sender, EventArgs e)
        {
            SingleSatAccessAnalysis();
        }

        private void VisibilityPredictCard_Load(object sender, EventArgs e)
        {
            dtpStartTime.Value = DateTime.Now;
            LoadSatelliteList();
        }

        /// <summary>
        /// 加载卫星列表到下拉框
        /// </summary>
        private void LoadSatelliteList()
        {
            try
            {
                // 确保XML文件存在
                SatelliteOrbitManager.EnsureXmlFileExists();

                // 获取所有卫星名称
                List<string> satelliteNames = SatelliteOrbitManager.GetAllSatelliteNames();

                // 清空并重新填充下拉框
                cbSatID.Items.Clear();
                foreach (string name in satelliteNames)
                {
                    cbSatID.Items.Add(name);
                }

                // 如果有卫星，选择第一个
                if (cbSatID.Items.Count > 0)
                {
                    cbSatID.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载卫星列表失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 设置轨根按钮点击事件
        /// </summary>
        private void btnSetOrbit_Click(object sender, EventArgs e)
        {
            try
            {
                string selectedSatellite = cbSatID.Text.Trim();
                if (string.IsNullOrEmpty(selectedSatellite))
                {
                    MessageBox.Show("请先选择卫星！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 获取轨道外推文件路径
                string orbFilePath = Path.Combine(Application.StartupPath, "02轨道外推", "orb_eph_orbit.ifm");

                // 将选中卫星的轨道根数写入文件
                if (SatelliteOrbitManager.WriteToOrbFile(selectedSatellite, orbFilePath))
                {
                    MessageBox.Show($"卫星 '{selectedSatellite}' 的轨道根数已设置成功！", "成功",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置轨道根数失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 卫星管理按钮点击事件
        /// </summary>
        private void btnSatelliteManagement_Click(object sender, EventArgs e)
        {
            try
            {
                using (SatelliteManagementForm form = new SatelliteManagementForm())
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // 刷新卫星列表
                        LoadSatelliteList();
                    }
                    else
                    {
                        // 即使用户取消，也刷新列表以防有变化
                        LoadSatelliteList();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开卫星管理窗口失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    /// <summary>
    /// 数学计算工具类
    /// </summary>
    public static class SatelliteMathUtils
    {
        private const double PI = Math.PI;
        public const double DEG_TO_RAD = PI / 180.0;
        public const double RAD_TO_DEG = 180.0 / PI;

        // WGS84椭球参数
        private const double EARTH_A = 6378140.0;      // 长半轴 (m)
        private const double EARTH_E = 8.1819221e-2;   // 偏心率

        /// <summary>
        /// 儒略日计算
        /// </summary>
        public static double Julian(int year, int month, int day, int hour, int minute, double second)
        {
            return 367 * year - Math.Floor(7.0 * (year + Math.Floor((month + 9.0) / 12.0)) / 4.0) +
                   Math.Floor(275.0 * month / 9.0) + day + 1721013.5 +
                   ((second / 60.0 + minute) / 60.0 + hour) / 24.0;
        }

        /// <summary>
        /// 大地坐标转地心地固坐标 (BLH -> ECEF)
        /// </summary>
        /// <param name="lat">纬度 (弧度)</param>
        /// <param name="lon">经度 (弧度)</param>
        /// <param name="height">高程 (米)</param>
        /// <returns>ECEF坐标 [x, y, z] (米)</returns>
        public static double[] Geodetic2ECEF(double lat, double lon, double height)
        {
            double N = EARTH_A / Math.Sqrt(1 - EARTH_E * EARTH_E * Math.Sin(lat) * Math.Sin(lat));
            double x = (N + height) * Math.Cos(lat) * Math.Cos(lon);
            double y = (N + height) * Math.Cos(lat) * Math.Sin(lon);
            double z = (N * (1 - EARTH_E * EARTH_E) + height) * Math.Sin(lat);
            return new double[] { x, y, z };
        }

        /// <summary>
        /// 计算测站坐标和转换矩阵
        /// </summary>
        /// <param name="lon">经度 (弧度)</param>
        /// <param name="lat">纬度 (弧度)</param>
        /// <param name="height">高程 (米)</param>
        /// <returns>测站ECEF坐标和地心系到测站平面坐标系转换矩阵</returns>
        public static (double[] stationECEF, double[,] transformMatrix) ComputeStationCoordinate(double lon, double lat, double height)
        {
            // 计算测站ECEF坐标
            double[] stationECEF = Geodetic2ECEF(lat, lon, height);

            // 计算地心系到测站平面坐标系的转换矩阵
            //double[,] transformMatrix = new double[3, 3]
            //{
            //    { -Math.Sin(lon), -Math.Sin(lat) * Math.Cos(lon), Math.Cos(lat) * Math.Cos(lon) },
            //    { Math.Cos(lon), -Math.Sin(lat) * Math.Sin(lon), Math.Cos(lat) * Math.Sin(lon) },
            //    { 0, Math.Cos(lat), Math.Sin(lat) }
            //};

            double[,] transformMatrixT = new double[3, 3]
            {
                { -Math.Sin(lon), Math.Cos(lon), 0},
                { -Math.Sin(lat) * Math.Cos(lon), -Math.Sin(lat) * Math.Sin(lon), Math.Cos(lat) },
                { Math.Cos(lat) * Math.Cos(lon), Math.Cos(lat) * Math.Sin(lon),  Math.Sin(lat) }
            };

            return (stationECEF, transformMatrixT);
        }

        /// <summary>
        /// 计算卫星相对测站的方位角、俯仰角和距离 (AER)
        /// </summary>
        /// <param name="satECEF">卫星ECEF坐标 [x, y, z] (米)</param>
        /// <param name="stationECEF">测站ECEF坐标 [x, y, z] (米)</param>
        /// <param name="transformMatrix">地心系到测站平面坐标系转换矩阵</param>
        /// <returns>方位角(弧度)、俯仰角(弧度)、距离(米)</returns>
        public static (double azimuth, double elevation, double range) ComputeSatAccessAER(
            double[] satECEF, double[] stationECEF, double[,] transformMatrix)
        {
            // 计算卫星相对测站的位置向量
            double[] relativePos = new double[3]
            {
                satECEF[0] - stationECEF[0],
                satECEF[1] - stationECEF[1],
                satECEF[2] - stationECEF[2]
            };

            // 转换到测站平面坐标系
            double[] localPos = new double[3];
            for (int i = 0; i < 3; i++)
            {
                localPos[i] = 0;
                for (int j = 0; j < 3; j++)
                {
                    localPos[i] += transformMatrix[i, j] * relativePos[j];
                }
            }

            double x = localPos[0];
            double y = localPos[1];
            double z = localPos[2];

            // 计算方位角
            double azimuth = Math.Atan2(x, y);
            if (azimuth < 0)
                azimuth += 2 * PI;

            // 计算俯仰角
            double tmp = Math.Sqrt(x * x + y * y);
            double elevation = Math.Atan2(z, tmp);

            // 计算距离
            double range = Math.Sqrt(relativePos[0] * relativePos[0] +
                                   relativePos[1] * relativePos[1] +
                                   relativePos[2] * relativePos[2]);

            return (azimuth, elevation, range);
        }
    }

    
}
