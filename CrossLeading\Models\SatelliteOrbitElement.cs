using System;
using System.Xml.Serialization;

namespace CrossLeading.Models
{
    /// <summary>
    /// 卫星轨道根数数据结构
    /// </summary>
    [Serializable]
    public class SatelliteOrbitElement
    {
        /// <summary>
        /// 卫星名称/ID
        /// </summary>
        [XmlAttribute("Name")]
        public string SatelliteName { get; set; }

        /// <summary>
        /// 历元年
        /// </summary>
        [XmlElement("Year")]
        public int Year { get; set; }

        /// <summary>
        /// 历元月
        /// </summary>
        [XmlElement("Month")]
        public int Month { get; set; }

        /// <summary>
        /// 历元日
        /// </summary>
        [XmlElement("Day")]
        public int Day { get; set; }

        /// <summary>
        /// 历元时
        /// </summary>
        [XmlElement("Hour")]
        public int Hour { get; set; }

        /// <summary>
        /// 历元分
        /// </summary>
        [XmlElement("Minute")]
        public int Minute { get; set; }

        /// <summary>
        /// 历元秒
        /// </summary>
        [XmlElement("Second")]
        public int Second { get; set; }

        /// <summary>
        /// 半长轴 (km)
        /// </summary>
        [XmlElement("SemiMajorAxis")]
        public double SemiMajorAxis { get; set; }

        /// <summary>
        /// 偏心率
        /// </summary>
        [XmlElement("Eccentricity")]
        public double Eccentricity { get; set; }

        /// <summary>
        /// 轨道倾角 (度)
        /// </summary>
        [XmlElement("Inclination")]
        public double Inclination { get; set; }

        /// <summary>
        /// 升交点赤经 (度)
        /// </summary>
        [XmlElement("RAAN")]
        public double RAAN { get; set; }

        /// <summary>
        /// 近地点幅角 (度)
        /// </summary>
        [XmlElement("ArgumentOfPerigee")]
        public double ArgumentOfPerigee { get; set; }

        /// <summary>
        /// 平近点角 (度)
        /// </summary>
        [XmlElement("MeanAnomaly")]
        public double MeanAnomaly { get; set; }

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public SatelliteOrbitElement()
        {
            SatelliteName = "";
            Year = DateTime.Now.Year;
            Month = DateTime.Now.Month;
            Day = DateTime.Now.Day;
            Hour = 0;
            Minute = 0;
            Second = 0;
            SemiMajorAxis = 7000.0;
            Eccentricity = 0.0;
            Inclination = 0.0;
            RAAN = 0.0;
            ArgumentOfPerigee = 0.0;
            MeanAnomaly = 0.0;
        }

        /// <summary>
        /// 带参数的构造函数
        /// </summary>
        public SatelliteOrbitElement(string satelliteName, int year, int month, int day, 
            int hour, int minute, int second, double semiMajorAxis, double eccentricity, 
            double inclination, double raan, double argumentOfPerigee, double meanAnomaly)
        {
            SatelliteName = satelliteName;
            Year = year;
            Month = month;
            Day = day;
            Hour = hour;
            Minute = minute;
            Second = second;
            SemiMajorAxis = semiMajorAxis;
            Eccentricity = eccentricity;
            Inclination = inclination;
            RAAN = raan;
            ArgumentOfPerigee = argumentOfPerigee;
            MeanAnomaly = meanAnomaly;
        }

        /// <summary>
        /// 从orb_eph_orbit.ifm格式字符串解析轨道根数
        /// 格式: 年 月 日 时 分 秒 半长轴 偏心率 倾角 升交点赤经 近地点幅角 平近点角
        /// </summary>
        /// <param name="satelliteName">卫星名称</param>
        /// <param name="orbData">轨道数据字符串</param>
        /// <returns>解析后的轨道根数对象</returns>
        public static SatelliteOrbitElement ParseFromOrbData(string satelliteName, string orbData)
        {
            try
            {
                string[] parts = orbData.Trim().Split(new char[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length < 12)
                {
                    throw new ArgumentException("轨道数据格式不正确，需要至少12个参数");
                }

                return new SatelliteOrbitElement(
                    satelliteName,
                    int.Parse(parts[0]),        // 年
                    int.Parse(parts[1]),        // 月
                    int.Parse(parts[2]),        // 日
                    int.Parse(parts[3]),        // 时
                    int.Parse(parts[4]),        // 分
                    int.Parse(parts[5]),        // 秒
                    double.Parse(parts[6]),     // 半长轴
                    double.Parse(parts[7]),     // 偏心率
                    double.Parse(parts[8]),     // 倾角
                    double.Parse(parts[9]),     // 升交点赤经
                    double.Parse(parts[10]),    // 近地点幅角
                    double.Parse(parts[11])     // 平近点角
                );
            }
            catch (Exception ex)
            {
                throw new ArgumentException($"解析轨道数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 转换为orb_eph_orbit.ifm格式字符串
        /// </summary>
        /// <returns>格式化的轨道数据字符串</returns>
        public string ToOrbDataString()
        {
            return $"{Year} {Month} {Day} {Hour:00} {Minute:00} {Second:00} " +
                   $"{SemiMajorAxis} {Eccentricity:F6} {Inclination:F4} " +
                   $"{RAAN:F4} {ArgumentOfPerigee:F4} {MeanAnomaly:F4}";
        }

        /// <summary>
        /// 获取历元时间
        /// </summary>
        /// <returns>历元DateTime对象</returns>
        public DateTime GetEpochTime()
        {
            return new DateTime(Year, Month, Day, Hour, Minute, Second);
        }

        /// <summary>
        /// 设置历元时间
        /// </summary>
        /// <param name="epochTime">历元时间</param>
        public void SetEpochTime(DateTime epochTime)
        {
            Year = epochTime.Year;
            Month = epochTime.Month;
            Day = epochTime.Day;
            Hour = epochTime.Hour;
            Minute = epochTime.Minute;
            Second = epochTime.Second;
        }

        /// <summary>
        /// 验证轨道根数的有效性
        /// </summary>
        /// <returns>验证结果和错误信息</returns>
        public (bool IsValid, string ErrorMessage) Validate()
        {
            if (string.IsNullOrWhiteSpace(SatelliteName))
                return (false, "卫星名称不能为空");

            if (Year < 1900 || Year > 2100)
                return (false, "年份必须在1900-2100之间");

            if (Month < 1 || Month > 12)
                return (false, "月份必须在1-12之间");

            if (Day < 1 || Day > 31)
                return (false, "日期必须在1-31之间");

            if (Hour < 0 || Hour > 23)
                return (false, "小时必须在0-23之间");

            if (Minute < 0 || Minute > 59)
                return (false, "分钟必须在0-59之间");

            if (Second < 0 || Second > 59)
                return (false, "秒数必须在0-59之间");

            if (SemiMajorAxis <= 0)
                return (false, "半长轴必须大于0");

            if (Eccentricity < 0 || Eccentricity >= 1)
                return (false, "偏心率必须在0-1之间");

            if (Inclination < 0 || Inclination > 180)
                return (false, "轨道倾角必须在0-180度之间");

            if (RAAN < 0 || RAAN >= 360)
                return (false, "升交点赤经必须在0-360度之间");

            if (ArgumentOfPerigee < 0 || ArgumentOfPerigee >= 360)
                return (false, "近地点幅角必须在0-360度之间");

            if (MeanAnomaly < 0 || MeanAnomaly >= 360)
                return (false, "平近点角必须在0-360度之间");

            return (true, "");
        }

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>卫星信息字符串</returns>
        public override string ToString()
        {
            return $"{SatelliteName} ({GetEpochTime():yyyy-MM-dd HH:mm:ss})";
        }
    }
}
