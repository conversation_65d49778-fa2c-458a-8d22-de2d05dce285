#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
W6E数据模拟器
模拟W6E测角数据，通过UDP组播发送
数据格式参考LeadingProcess.cs中的FormtW6E和W6EData结构体
"""

import socket
import struct
import time
import math
from datetime import datetime, timedelta
import threading

class W6ESimulator:
    def __init__(self, multicast_ip="***************", port=8080):
        """
        初始化W6E数据模拟器
        
        Args:
            multicast_ip: 组播地址
            port: 端口号
        """
        self.multicast_ip = multicast_ip
        self.port = port
        self.socket = None
        self.running = False
        
        # 模拟参数
        self.start_time = datetime.now()
        self.az_start = 45.0  # 起始方位角（度）
        self.el_start = 30.0  # 起始俯仰角（度）
        self.az_rate = 2.0    # 方位角变化率（度/秒）
        self.el_rate = 1.0    # 俯仰角变化率（度/秒）
        
        # EMBL包头固定值
        self.MID = 0x12345678  # 任务标识
        self.BID = 0x00100605  # 信息类别 (W6E数据标识)
        self.Res1 = 0x00000000  # 保留字段1
        self.Res2 = 0x00000000  # 保留字段2
        self.LEN = 23          # W6E数据长度（23字节）
        
    def setup_socket(self):
        """设置UDP组播socket"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

            # 设置组播TTL
            self.socket.setsockopt(socket.IPPROTO_IP, socket.IP_MULTICAST_TTL, 2)

            # 设置组播接口（绑定到本地接口）
            try:
                # 获取本地IP地址
                import socket as sock_module
                hostname = sock_module.gethostname()
                local_ip = sock_module.gethostbyname(hostname)
                print(f"本地IP地址: {local_ip}")

                # 设置组播发送接口
                self.socket.setsockopt(socket.IPPROTO_IP, socket.IP_MULTICAST_IF,
                                     socket.inet_aton(local_ip))
                print(f"组播发送接口已设置为: {local_ip}")
            except Exception as e:
                print(f"设置组播接口警告: {e}")
                print("使用默认接口")

            print(f"UDP组播socket已设置: {self.multicast_ip}:{self.port}")
            return True
        except Exception as e:
            print(f"设置socket失败: {e}")
            return False
    
    def get_current_js_time(self):
        """
        获取当前时间的积秒值（以0.1毫秒为单位）
        积秒 = ((时*60 + 分)*60 + 秒)*1000 + 毫秒) * 10
        """
        now = datetime.now()
        total_ms = (now.hour * 3600 + now.minute * 60 + now.second) * 1000 + now.microsecond // 1000
        return int(total_ms * 10)
    
    def degrees_to_w6e_format(self, degrees):
        """
        将角度转换为W6E格式的uint32值
        根据公式: degrees = tmp / (2^32) * 360
        反推: tmp = degrees * (2^32) / 360
        """
        if degrees < 0:
            degrees += 360  # 确保角度为正值
        
        # 限制角度范围
        degrees = degrees % 360
        
        # 转换为W6E格式
        tmp = int(degrees * (2**32) / 360)
        return tmp & 0xFFFFFFFF  # 确保是32位无符号整数
    
    def calculate_current_angles(self, elapsed_seconds):
        """
        计算当前时刻的方位角和俯仰角
        使用连续变化的角度，模拟真实的跟踪运动
        """
        # 方位角：线性变化 + 小幅正弦波动
        az = self.az_start + self.az_rate * elapsed_seconds + 2 * math.sin(elapsed_seconds * 0.1)
        
        # 俯仰角：线性变化 + 小幅余弦波动
        el = self.el_start + self.el_rate * elapsed_seconds + 1 * math.cos(elapsed_seconds * 0.15)
        
        # 限制俯仰角范围 [0, 90]
        el = max(0, min(90, el))
        
        return az, el
    
    def create_w6e_packet(self, elapsed_seconds):
        """
        创建完整的W6E数据包
        包含EMBL包头(24字节) + W6E数据(23字节)

        W6E数据格式（23字节）：
        1字节的状态
        4字节的时标
        4字节的AZ
        4字节的EL
        2字节的AZ滞后值
        2字节的EL滞后值
        2字节的AZ光脱靶值
        2字节的EL光脱靶值
        2字节的信噪比
        """
        # 获取当前时间戳
        current_time = self.get_current_js_time()

        # 计算当前角度
        az_degrees, el_degrees = self.calculate_current_angles(elapsed_seconds)

        # 转换角度为W6E格式
        az_w6e = self.degrees_to_w6e_format(az_degrees)
        el_w6e = self.degrees_to_w6e_format(el_degrees)

        # 构建EMBL包头 (24字节)
        embl_header = struct.pack('<6I',
                                 current_time,  # Time (积秒)
                                 self.MID,      # MID (任务标识)
                                 self.BID,      # BID (信息类别)
                                 self.Res1,     # Res1 (保留字段1)
                                 self.Res2,     # Res2 (保留字段2)
                                 self.LEN)      # LEN (数据长度)

        # 构建W6E数据部分 (23字节)
        status_byte = 0x02  # 状态字节 (0x02=自跟踪, 0x00=非自跟踪)

        # 模拟滞后值、脱靶值和信噪比
        az_lag = int(0.1 * 1000)      # AZ滞后值 (0.1度转换为0.001度单位)
        el_lag = int(0.05 * 1000)     # EL滞后值 (0.05度转换为0.001度单位)
        az_offset = int(0.02 * 1000)  # AZ光脱靶值 (0.02度转换为0.001度单位)
        el_offset = int(0.01 * 1000)  # EL光脱靶值 (0.01度转换为0.001度单位)
        snr = 85                      # 信噪比 (85dB)

        # 确保值在16位有符号整数范围内
        az_lag = max(-32768, min(32767, az_lag))
        el_lag = max(-32768, min(32767, el_lag))
        az_offset = max(-32768, min(32767, az_offset))
        el_offset = max(-32768, min(32767, el_offset))
        snr = max(0, min(65535, snr))

        # 打包W6E数据：1+4+4+4+2+2+2+2+2 = 23字节
        w6e_data = struct.pack('<BI2I5h',
                              status_byte,    # 1字节状态
                              current_time,   # 4字节时标
                              az_w6e,         # 4字节AZ
                              el_w6e,         # 4字节EL
                              az_lag,         # 2字节AZ滞后值
                              el_lag,         # 2字节EL滞后值
                              az_offset,      # 2字节AZ光脱靶值
                              el_offset,      # 2字节EL光脱靶值
                              snr)            # 2字节信噪比

        # 组合完整数据包
        packet = embl_header + w6e_data

        # 打印调试信息
        print(f"时间: {datetime.now().strftime('%H:%M:%S.%f')[:-3]}, "
              f"方位: {az_degrees:.3f}°, 俯仰: {el_degrees:.3f}°, "
              f"积秒: {current_time}, 信噪比: {snr}dB")

        return packet
    
    def send_data(self, duration_seconds=30, interval_seconds=0.05):
        """
        发送W6E数据
        
        Args:
            duration_seconds: 模拟时长（秒）
            interval_seconds: 发送间隔（秒）
        """
        if not self.setup_socket():
            return
        
        self.running = True
        start_time = time.time()
        next_send_time = start_time
        
        print(f"开始发送W6E数据模拟...")
        print(f"组播地址: {self.multicast_ip}:{self.port}")
        print(f"发送间隔: {interval_seconds}秒")
        print(f"模拟时长: {duration_seconds}秒")
        print("-" * 60)
        
        try:
            while self.running and (time.time() - start_time) < duration_seconds:
                current_time = time.time()
                
                if current_time >= next_send_time:
                    elapsed_seconds = current_time - start_time
                    packet = self.create_w6e_packet(elapsed_seconds)
                    
                    # 发送数据包
                    self.socket.sendto(packet, (self.multicast_ip, self.port))
                    
                    # 计算下次发送时间
                    next_send_time += interval_seconds
                
                # 短暂休眠避免CPU占用过高
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\n收到中断信号，停止发送...")
        except Exception as e:
            print(f"发送数据时出错: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """停止模拟器"""
        self.running = False
        if self.socket:
            self.socket.close()
            self.socket = None
        print("W6E数据模拟器已停止")

def main():
    """主函数"""
    print("W6E数据模拟器")
    print("=" * 50)
    
    # 可以根据需要修改这些参数
    multicast_ip = "***************"  # 组播地址
    port = 12345                       # 端口号
    
    # 创建模拟器实例
    simulator = W6ESimulator(multicast_ip, port)
    
    # 设置模拟参数
    simulator.az_start = 45.0   # 起始方位角
    simulator.el_start = 30.0   # 起始俯仰角
    simulator.az_rate = 2.0     # 方位角变化率（度/秒）
    simulator.el_rate = 1.0     # 俯仰角变化率（度/秒）
    
    try:
        # 开始发送数据：30秒时长，每0.05秒发送一次
        simulator.send_data(duration_seconds=30, interval_seconds=0.05)
    except Exception as e:
        print(f"运行出错: {e}")
    
    print("模拟完成")

if __name__ == "__main__":
    main()
