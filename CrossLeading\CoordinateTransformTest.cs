using CrossLeading;
using CrossLeading.Cards;
using CrossLeading.Models;
using MyListBox;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using System.Xml;
using static System.Collections.Specialized.BitVector32;

namespace CrossLeading.Test
{
    /// <summary>
    /// 坐标变换算法测试程序
    /// </summary>
    class CoordinateTransformTest
    {
        // 测试参数
        private static readonly double ERROR_THRESHOLD = 10.0; // 误差阈值(米)
        private static readonly double TIME_SPAN_MINUTES = 30.0; // 测试时间跨度(分钟)
        private static readonly double TIME_STEP_SECONDS = 60.0; // 时间步长(秒)
        
        // 测试站点配置(使用默认测站)
        private static readonly DevConf TEST_STATION = new DevConf
        {
            devName = "TEST-STATION",
            lati = 46.747872,  // 纬度(度)
            longi = 130.290212, // 经度(度)
            height = 128.811   // 高程(米)
        };

        public static void RunTest()
        {
            Console.WriteLine("=== 坐标变换算法测试程序 ===");
            Console.WriteLine($"误差阈值: {ERROR_THRESHOLD} 米");
            Console.WriteLine($"测试时间跨度: {TIME_SPAN_MINUTES} 分钟");
            Console.WriteLine($"时间步长: {TIME_STEP_SECONDS} 秒");
            Console.WriteLine($"测试站点: {TEST_STATION.devName} ({TEST_STATION.lati}°, {TEST_STATION.longi}°, {TEST_STATION.height}m)");
            Console.WriteLine();

            try
            {
                // 1. 读取卫星轨道根数
                List<SatelliteOrbitElement> satellites = ReadSatelliteOrbits();
                Console.WriteLine($"读取到 {satellites.Count} 个卫星轨道根数");

                int passCount = 0;
                int totalCount = satellites.Count;

                // 2. 逐个测试每个卫星
                foreach (var satellite in satellites)
                {
                    Console.WriteLine($"\n--- 测试卫星: {satellite.SatelliteName} ---");

                    if (TestSatelliteCoordinateTransform(satellite))
                    {
                        Console.WriteLine($"✓ {satellite.SatelliteName} 坐标变换测试通过");
                        passCount++;
                    }
                    else
                    {
                        Console.WriteLine($"✗ {satellite.SatelliteName} 坐标变换测试失败");
                    }
                }

                // 3. 输出测试结果
                Console.WriteLine("\n=== 测试结果汇总 ===");
                Console.WriteLine($"总测试数量: {totalCount}");
                Console.WriteLine($"通过数量: {passCount}");
                Console.WriteLine($"失败数量: {totalCount - passCount}");
                Console.WriteLine($"通过率: {(double)passCount / totalCount * 100:F1}%");

                
                if (passCount == totalCount)
                {
                    Console.WriteLine("🎉 所有测试通过！坐标变换算法正确。");
                }
                else
                {
                    Console.WriteLine("⚠️ 部分测试失败，请检查坐标变换算法。");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex.StackTrace}");
            }

            Console.WriteLine("\n按任意键继续...");
            Console.ReadKey();
        }

        /// <summary>
        /// 读取卫星轨道根数
        /// </summary>
        private static List<SatelliteOrbitElement> ReadSatelliteOrbits()
        {
            // 直接使用SatelliteOrbitManager加载所有卫星
            List<SatelliteOrbitElement> satellites = SatelliteOrbitManager.LoadAllSatellites();

            if (satellites.Count == 0)
            {
                throw new InvalidOperationException("未找到任何卫星轨道数据，请先添加卫星数据");
            }

            return satellites;
        }

        /// <summary>
        /// 测试单个卫星的坐标变换
        /// </summary>
        private static bool TestSatelliteCoordinateTransform(SatelliteOrbitElement satellite)
        {
            try
            {
                Console.WriteLine($"  开始测试卫星: {satellite.SatelliteName}");

                // 使用简化的测试方法，不依赖外部轨道外推程序
                //return PerformSimplifiedCoordinateTransformTest(satellite);

                // 
                return PerformOriginCoodinateTransformTest(satellite);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  测试异常: {ex.Message}");
                return false;
            }
        }


        private static bool PerformOriginCoodinateTransformTest(SatelliteOrbitElement satellite)
        {
            string workingDir = Path.Combine(Application.StartupPath, "02轨道外推");
            // 1. 写入轨道根数
            string orbitData = $"{satellite.Year:D4} {satellite.Month:D2} {satellite.Day:D2} " +
                             $"{satellite.Hour:D2} {satellite.Minute:D2} {satellite.Second:D2} " +
                             $"{satellite.SemiMajorAxis} {satellite.Eccentricity} {satellite.Inclination} " +
                             $"{satellite.RAAN} {satellite.ArgumentOfPerigee} {satellite.MeanAnomaly}";

            string orbInFilePath = Path.Combine(workingDir, "orb_eph_orbit.ifm");
            string orbOutFilePath = Path.Combine(workingDir,  "orb_eph_start.ifm");

            File.WriteAllText(orbInFilePath, orbitData);

            // 2. 写入计算跨度
            DateTime startTime = new DateTime(satellite.Year, satellite.Month, satellite.Day, 
                satellite.Hour, satellite.Minute, satellite.Second);
            VisibilityPredictCard.ProcessFiles(orbInFilePath, orbOutFilePath, startTime, TIME_SPAN_MINUTES, TIME_STEP_SECONDS);

            // 3. 调用orb_eph.exe
            string orbExePath = Path.Combine(workingDir, "orb_eph.exe");
            if (!File.Exists(orbExePath))
            {
                Console.WriteLine($"轨道外推程序不存在：{orbExePath}");
            }

            ProcessStartInfo pinfo = new ProcessStartInfo
            {
                FileName = orbExePath,
                WorkingDirectory = workingDir,
                UseShellExecute = false,
                CreateNoWindow = true,
                RedirectStandardOutput = true,
                RedirectStandardError = true
            };

            using (Process process = Process.Start(pinfo))
            {
                process.WaitForExit();

                if (!process.HasExited)
                {
                    process.Kill();
                    Console.WriteLine("轨道外推计算超时！");
                    return false;
                }
                if (process.ExitCode != 0 && process.ExitCode != 1)
                {
                    Console.WriteLine($"轨道外推计算失败，退出代码: {process.ExitCode}");
                    return false;
                }
            }

            // 4. 读取orb_ecf.txt，转换为被测站的RAE
            string ephEcfPath = Path.Combine(workingDir, "eph_ecf.txt");
            if (!File.Exists(ephEcfPath))
            {
                Console.WriteLine($"轨道外推文件不存在{ephEcfPath}");
                return false;
            }

            // 读取轨道数据
            var satPosData = VisibilityPredictCard.ReadOrbitData(ephEcfPath);
            if (satPosData.Count == 0)
            {
                Console.WriteLine("未读取到有效的轨道数据！");
                return false;
            }

            var visibilityData = new List<StationVisibilityData>();

            // 将测站坐标转换为弧度
            double stationLon = TEST_STATION.longi * SatelliteMathUtils.DEG_TO_RAD;
            double stationLat = TEST_STATION.lati * SatelliteMathUtils.DEG_TO_RAD;
            double stationHeight = TEST_STATION.height;

            // 计算测站坐标和转换矩阵
            var (stationECEF, transformMatrix) = SatelliteMathUtils.ComputeStationCoordinate(
                stationLon, stationLat, stationHeight);

            List<StationVisibilityData> staRAE = new List<StationVisibilityData>();
            foreach (var satPos in satPosData)
            {
                // 卫星ECEF坐标
                double[] satECEF = { satPos.X, satPos.Y, satPos.Z };

                // 计算AER
                var (azimuth, elevation, range) = SatelliteMathUtils.ComputeSatAccessAER(
                    satECEF, stationECEF, transformMatrix);

                StationVisibilityData stationVisibilityData = new StationVisibilityData
                {
                    Azimuth = azimuth * 180 / Math.PI,
                    Elevation = elevation * 180 / Math.PI,
                    Range = range,
                    IsVisible = false,
                    Time = satPos.Time
                };
                staRAE.Add(stationVisibilityData);
            }

            // 5. 反向转换为XYZ，与orb_ecf.txt进行比较
            int errorCount = 0;
            double totalError = 0;
            double maxError = 0;

            for (int i = 0; i < staRAE.Count; i++)
            {
                var rae = staRAE[i];
                double R = rae.Range;
                double El = rae.Elevation;
                double Az = rae.Azimuth;

                double[] reconstructedXYZ = ConvertAERToXYZ(new double[] { R, Az, El }, TEST_STATION);

                var satPos = satPosData[i];
                double[] originalXYZ = new double[] { satPos.X, satPos.Y, satPos.Z };

                // 计算误差
                double error = CalculatePositionError(originalXYZ, reconstructedXYZ);
                totalError += error;

                if (error > maxError)
                {
                    maxError = error;
                }

                if (error > ERROR_THRESHOLD)
                {
                    errorCount++;
                    Console.WriteLine($"    时间: {rae.Time:yyyy-MM-dd HH:mm:ss}, 误差: {error:F3}m (超出阈值)");
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 执行简化的坐标变换测试
        /// </summary>
        private static bool PerformSimplifiedCoordinateTransformTest(SatelliteOrbitElement satellite)
        {
            // 生成一些测试轨道点
            List<SatelliteOrbitData> testPoints = GenerateTestOrbitPoints(satellite);
            Console.WriteLine($"  生成了 {testPoints.Count} 个测试轨道点");

            int errorCount = 0;
            double totalError = 0;
            double maxError = 0;

            foreach (var point in testPoints)
            {
                // 原始XYZ坐标
                double[] originalXYZ = { point.X, point.Y, point.Z };

                // 步骤1: XYZ → AER (使用现有的坐标变换方法)
                double[] aer = ConvertXYZToAER(originalXYZ, TEST_STATION);

                // 步骤2: AER → XYZ (使用现有的坐标变换方法)
                double[] reconstructedXYZ = ConvertAERToXYZ(aer, TEST_STATION);

                // 计算误差
                double error = CalculatePositionError(originalXYZ, reconstructedXYZ);
                totalError += error;

                if (error > maxError)
                {
                    maxError = error;
                }

                if (error > ERROR_THRESHOLD)
                {
                    errorCount++;
                    Console.WriteLine($"    时间: {point.Time:yyyy-MM-dd HH:mm:ss}, 误差: {error:F3}m (超出阈值)");
                }
            }

            double avgError = totalError / testPoints.Count;
            Console.WriteLine($"  平均误差: {avgError:F3}m, 最大误差: {maxError:F3}m, 超阈值点数: {errorCount}");

            return errorCount == 0;
        }

        /// <summary>
        /// 生成测试轨道点
        /// </summary>
        private static List<SatelliteOrbitData> GenerateTestOrbitPoints(SatelliteOrbitElement satellite)
        {
            var points = new List<SatelliteOrbitData>();
            DateTime startTime = satellite.GetEpochTime();

            // 基于轨道根数计算一些典型的轨道位置
            double a = satellite.SemiMajorAxis; // 半长轴
            double e = satellite.Eccentricity;  // 偏心率
            double i = satellite.Inclination * Math.PI / 180.0; // 倾角(转弧度)

            // 生成几个轨道点进行测试
            for (int j = 0; j < 8; j++)
            {
                double meanAnomaly = j * Math.PI / 4; // 每45度一个点

                // 简化的轨道计算 - 生成圆形轨道上的点
                double x = a * Math.Cos(meanAnomaly);
                double y = a * Math.Sin(meanAnomaly) * Math.Cos(i);
                double z = a * Math.Sin(meanAnomaly) * Math.Sin(i);

                points.Add(new SatelliteOrbitData
                {
                    Time = startTime.AddMinutes(j * 15), // 每15分钟一个点
                    X = x,
                    Y = y,
                    Z = z,
                    Vx = 0, Vy = 0, Vz = 0 // 简化，不计算速度
                });
            }

            return points;
        }

        /// <summary>
        /// XYZ转AER (使用现有的坐标变换方法)
        /// </summary>
        private static double[] ConvertXYZToAER(double[] satXYZ, DevConf station)
        {
            try
            {
                // 使用OrbitForcast类中的坐标变换方法
                double[] stationBLH = { station.lati, station.longi, station.height };
                double[] satXYZ6 = { satXYZ[0], satXYZ[1], satXYZ[2], 0, 0, 0 };

                // 地心坐标系 → 测站坐标系
                double[] staXYZ = OrbitForcast.Gxyz2staRxyz(satXYZ6, stationBLH);

                // 测站坐标系 → 极坐标系
                double[] staXYZ6 = { staXYZ[0], staXYZ[1], staXYZ[2], staXYZ[3], staXYZ[4], staXYZ[5] };
                double[] rae = OrbitForcast.staRxyz2RAE(staXYZ6);

                return new double[] { rae[0], rae[1], rae[2] }; // R, A, E
            }
            catch (Exception ex)
            {
                Console.WriteLine($"    XYZ转AER异常: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// AER转XYZ (使用现有的坐标变换方法)
        /// </summary>
        private static double[] ConvertAERToXYZ(double[] aer, DevConf station)
        {
            try
            {
                // 构造RAE数组
                double[] RAE = new double[6];
                RAE[0] = aer[0]; // 距离(米)
                RAE[1] = aer[1]; // 方位角(度)
                RAE[2] = aer[2]; // 俯仰角(度)
                RAE[3] = 0; RAE[4] = 0; RAE[5] = 0; // 速度分量设为0

                // 测站BLH
                double[] SourceBLH = { station.lati, station.longi, station.height };

                // 坐标变换: RAE → staXYZ → Gxyz
                double[] staXYZ = OrbitForcast.RAE2staRxyz(RAE);
                double[] Gxyz = OrbitForcast.staRxyz2Gxyz(staXYZ, SourceBLH);

                return new double[] { Gxyz[0], Gxyz[1], Gxyz[2] };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"    AER转XYZ异常: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 计算位置误差
        /// </summary>
        private static double CalculatePositionError(double[] xyz1, double[] xyz2)
        {
            double dx = xyz1[0] - xyz2[0];
            double dy = xyz1[1] - xyz2[1];
            double dz = xyz1[2] - xyz2[2];

            return Math.Sqrt(dx * dx + dy * dy + dz * dz);
        }
    }
}