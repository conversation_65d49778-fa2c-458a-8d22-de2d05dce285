﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace CrossLeading.Utils
{
    public class Utilities
    {
        public static bool IsChildOf(Control child, Control parent)
        {
            if (child == null || child == parent)
                return false;
            if (child.Parent == parent)
                return true;
            return IsChildOf(child.Parent, parent);
        }
    }
}
