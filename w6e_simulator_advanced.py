#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
W6E数据模拟器 - 增强版
支持配置文件、命令行参数和更多自定义选项
"""

import socket
import struct
import time
import math
import json
import argparse
from datetime import datetime, timedelta
import threading

class W6ESimulatorAdvanced:
    def __init__(self, config_file=None):
        """
        初始化W6E数据模拟器
        
        Args:
            config_file: 配置文件路径
        """
        self.socket = None
        self.running = False
        
        # 加载配置
        self.load_config(config_file)
        
    def load_config(self, config_file):
        """加载配置文件"""
        # 默认配置
        default_config = {
            "network": {
                "multicast_ip": "***************",
                "port": 8080
            },
            "simulation": {
                "duration_seconds": 30,
                "interval_seconds": 20
            },
            "angles": {
                "az_start": 45.0,
                "el_start": 30.0,
                "az_rate": 2.0,
                "el_rate": 1.0
            },
            "embl_header": {
                "MID": "0x12345678",
                "BID": "0x00100605",
                "Res1": "0x00000000",
                "Res2": "0x00000000"
            },
            "advanced": {
                "sine_amplitude_az": 2.0,
                "sine_frequency_az": 0.1,
                "cosine_amplitude_el": 1.0,
                "cosine_frequency_el": 0.15
            }
        }
        
        # 尝试加载配置文件
        if config_file:
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    # 合并配置
                    self.merge_config(default_config, user_config)
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                print("使用默认配置")
        
        # 应用配置
        self.config = default_config
        self.apply_config()
    
    def merge_config(self, default, user):
        """递归合并配置"""
        for key, value in user.items():
            if key in default:
                if isinstance(value, dict) and isinstance(default[key], dict):
                    self.merge_config(default[key], value)
                else:
                    default[key] = value
    
    def apply_config(self):
        """应用配置到实例变量"""
        # 网络配置
        self.multicast_ip = self.config["network"]["multicast_ip"]
        self.port = self.config["network"]["port"]
        
        # 模拟配置
        self.duration_seconds = self.config["simulation"]["duration_seconds"]
        self.interval_seconds = self.config["simulation"]["interval_seconds"]
        
        # 角度配置
        self.az_start = self.config["angles"]["az_start"]
        self.el_start = self.config["angles"]["el_start"]
        self.az_rate = self.config["angles"]["az_rate"]
        self.el_rate = self.config["angles"]["el_rate"]
        
        # EMBL包头配置
        self.MID = int(self.config["embl_header"]["MID"], 16)
        self.BID = int(self.config["embl_header"]["BID"], 16)
        self.Res1 = int(self.config["embl_header"]["Res1"], 16)
        self.Res2 = int(self.config["embl_header"]["Res2"], 16)
        self.LEN = 23
        
        # 高级配置
        self.sine_amp_az = self.config["advanced"]["sine_amplitude_az"]
        self.sine_freq_az = self.config["advanced"]["sine_frequency_az"]
        self.cos_amp_el = self.config["advanced"]["cosine_amplitude_el"]
        self.cos_freq_el = self.config["advanced"]["cosine_frequency_el"]
        
    def setup_socket(self):
        """设置UDP组播socket"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.socket.setsockopt(socket.IPPROTO_IP, socket.IP_MULTICAST_TTL, 2)
            
            print(f"UDP组播socket已设置: {self.multicast_ip}:{self.port}")
            return True
        except Exception as e:
            print(f"设置socket失败: {e}")
            return False
    
    def get_current_js_time(self):
        """获取当前时间的积秒值（以0.1毫秒为单位）"""
        now = datetime.now()
        total_ms = (now.hour * 3600 + now.minute * 60 + now.second) * 1000 + now.microsecond // 1000
        return int(total_ms * 10)
    
    def degrees_to_w6e_format(self, degrees):
        """将角度转换为W6E格式的uint32值"""
        if degrees < 0:
            degrees += 360
        degrees = degrees % 360
        tmp = int(degrees * (2**32) / 360)
        return tmp & 0xFFFFFFFF
    
    def calculate_current_angles(self, elapsed_seconds):
        """计算当前时刻的方位角和俯仰角"""
        # 方位角：线性变化 + 正弦波动
        az = (self.az_start + self.az_rate * elapsed_seconds + 
              self.sine_amp_az * math.sin(elapsed_seconds * self.sine_freq_az))
        
        # 俯仰角：线性变化 + 余弦波动
        el = (self.el_start + self.el_rate * elapsed_seconds + 
              self.cos_amp_el * math.cos(elapsed_seconds * self.cos_freq_el))
        
        # 限制俯仰角范围 [0, 90]
        el = max(0, min(90, el))
        
        return az, el
    
    def create_w6e_packet(self, elapsed_seconds):
        """
        创建完整的W6E数据包

        W6E数据格式（23字节）：
        1字节的状态
        4字节的时标
        4字节的AZ
        4字节的EL
        2字节的AZ滞后值
        2字节的EL滞后值
        2字节的AZ光脱靶值
        2字节的EL光脱靶值
        2字节的信噪比
        """
        current_time = self.get_current_js_time()
        az_degrees, el_degrees = self.calculate_current_angles(elapsed_seconds)

        # 转换角度为W6E格式
        az_w6e = self.degrees_to_w6e_format(az_degrees)
        el_w6e = self.degrees_to_w6e_format(el_degrees)

        # 构建EMBL包头 (24字节)
        embl_header = struct.pack('<6I',
                                 current_time, self.MID, self.BID,
                                 self.Res1, self.Res2, self.LEN)

        # 模拟滞后值、脱靶值和信噪比
        az_lag = int(0.1 * 1000)      # AZ滞后值 (0.1度转换为0.001度单位)
        el_lag = int(0.05 * 1000)     # EL滞后值 (0.05度转换为0.001度单位)
        az_offset = int(0.02 * 1000)  # AZ光脱靶值 (0.02度转换为0.001度单位)
        el_offset = int(0.01 * 1000)  # EL光脱靶值 (0.01度转换为0.001度单位)
        snr = 85                      # 信噪比 (85dB)

        # 确保值在16位有符号整数范围内
        az_lag = max(-32768, min(32767, az_lag))
        el_lag = max(-32768, min(32767, el_lag))
        az_offset = max(-32768, min(32767, az_offset))
        el_offset = max(-32768, min(32767, el_offset))
        snr = max(0, min(65535, snr))

        # 构建W6E数据部分 (23字节)
        status_byte = 0x02  # 自跟踪状态
        w6e_data = struct.pack('<BI2I5h',
                              status_byte,    # 1字节状态
                              current_time,   # 4字节时标
                              az_w6e,         # 4字节AZ
                              el_w6e,         # 4字节EL
                              az_lag,         # 2字节AZ滞后值
                              el_lag,         # 2字节EL滞后值
                              az_offset,      # 2字节AZ光脱靶值
                              el_offset,      # 2字节EL光脱靶值
                              snr)            # 2字节信噪比

        # 打印调试信息
        print(f"时间: {datetime.now().strftime('%H:%M:%S.%f')[:-3]}, "
              f"方位: {az_degrees:.3f}°, 俯仰: {el_degrees:.3f}°, "
              f"积秒: {current_time}, 信噪比: {snr}dB")

        return embl_header + w6e_data
    
    def send_data(self):
        """发送W6E数据"""
        if not self.setup_socket():
            return
        
        self.running = True
        start_time = time.time()
        next_send_time = start_time
        
        print(f"开始发送W6E数据模拟...")
        print(f"组播地址: {self.multicast_ip}:{self.port}")
        print(f"发送间隔: {self.interval_seconds}秒")
        print(f"模拟时长: {self.duration_seconds}秒")
        print("-" * 60)
        
        try:
            while self.running and (time.time() - start_time) < self.duration_seconds:
                current_time = time.time()
                
                if current_time >= next_send_time:
                    elapsed_seconds = current_time - start_time
                    packet = self.create_w6e_packet(elapsed_seconds)
                    
                    # 发送数据包
                    self.socket.sendto(packet, (self.multicast_ip, self.port))
                    
                    # 计算下次发送时间
                    next_send_time += self.interval_seconds
                
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\n收到中断信号，停止发送...")
        except Exception as e:
            print(f"发送数据时出错: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """停止模拟器"""
        self.running = False
        if self.socket:
            self.socket.close()
            self.socket = None
        print("W6E数据模拟器已停止")
    
    def print_config(self):
        """打印当前配置"""
        print("当前配置:")
        print(json.dumps(self.config, indent=2, ensure_ascii=False))

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='W6E数据模拟器 - 增强版')
    parser.add_argument('-c', '--config', help='配置文件路径')
    parser.add_argument('--show-config', action='store_true', help='显示当前配置')
    parser.add_argument('--multicast-ip', help='组播地址')
    parser.add_argument('--port', type=int, help='端口号')
    parser.add_argument('--duration', type=int, help='模拟时长（秒）')
    parser.add_argument('--interval', type=int, help='发送间隔（秒）')
    
    args = parser.parse_args()
    
    print("W6E数据模拟器 - 增强版")
    print("=" * 50)
    
    # 创建模拟器实例
    simulator = W6ESimulatorAdvanced(args.config)
    
    # 应用命令行参数覆盖
    if args.multicast_ip:
        simulator.multicast_ip = args.multicast_ip
    if args.port:
        simulator.port = args.port
    if args.duration:
        simulator.duration_seconds = args.duration
    if args.interval:
        simulator.interval_seconds = args.interval
    
    # 显示配置
    if args.show_config:
        simulator.print_config()
        return
    
    try:
        # 开始发送数据
        simulator.send_data()
    except Exception as e:
        print(f"运行出错: {e}")
    
    print("模拟完成")

if __name__ == "__main__":
    main()
