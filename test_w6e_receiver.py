#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
W6E数据接收测试器
用于接收和解析W6E模拟器发送的数据，验证数据格式的正确性
"""

import socket
import struct
import threading
import time
from datetime import datetime

class W6EReceiver:
    def __init__(self, multicast_ip="***************", port=8080):
        """
        初始化W6E数据接收器
        
        Args:
            multicast_ip: 组播地址
            port: 端口号
        """
        self.multicast_ip = multicast_ip
        self.port = port
        self.socket = None
        self.running = False
        
    def setup_socket(self):
        """设置UDP组播接收socket"""
        try:
            # 创建socket
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            
            # 绑定到所有接口
            self.socket.bind(('', self.port))
            
            # 加入组播组
            mreq = struct.pack("4sl", socket.inet_aton(self.multicast_ip), socket.INADDR_ANY)
            self.socket.setsockopt(socket.IPPROTO_IP, socket.IP_ADD_MEMBERSHIP, mreq)
            
            print(f"UDP组播接收器已设置: {self.multicast_ip}:{self.port}")
            return True
        except Exception as e:
            print(f"设置接收socket失败: {e}")
            return False
    
    def js_to_hms(self, js_time):
        """
        将积秒转换为时分秒格式
        
        Args:
            js_time: 积秒值（0.1毫秒单位）
            
        Returns:
            tuple: (小时, 分钟, 秒, 毫秒)
        """
        if js_time < 0:
            return (0, 0, 0, 0.0)
        
        temp = js_time / 10  # 转换为毫秒
        temp_int = int(temp)
        
        hour = temp_int // 3600000
        minute = (temp_int % 3600000) // 60000
        second = (temp_int % 60000) // 1000
        ms = (js_time % 10000) / 10
        
        return (hour, minute, second, ms)
    
    def w6e_to_degrees(self, raw_value):
        """
        将W6E原始值转换为角度
        
        Args:
            raw_value: W6E格式的原始值
            
        Returns:
            float: 角度值
        """
        return raw_value / (2**32) * 360
    
    def parse_embl_header(self, data):
        """
        解析EMBL包头
        
        Args:
            data: 包头数据（24字节）
            
        Returns:
            dict: 包头信息
        """
        if len(data) < 24:
            return None
        
        header = struct.unpack('<6I', data[:24])
        
        return {
            'Time': header[0],
            'MID': header[1],
            'BID': header[2],
            'Res1': header[3],
            'Res2': header[4],
            'LEN': header[5]
        }
    
    def parse_w6e_data(self, data):
        """
        解析W6E数据部分

        W6E数据格式（23字节）：
        1字节的状态
        4字节的时标
        4字节的AZ
        4字节的EL
        2字节的AZ滞后值
        2字节的EL滞后值
        2字节的AZ光脱靶值
        2字节的EL光脱靶值
        2字节的信噪比

        Args:
            data: W6E数据（23字节）

        Returns:
            dict: W6E数据信息
        """
        if len(data) < 23:
            return None

        # 解析状态字节
        status_byte = data[0]
        is_tracking = (status_byte & 0x02) == 0x02

        # 解析完整的W6E数据：1+4+4+4+2+2+2+2+2 = 23字节
        unpacked = struct.unpack('<BI2I5h', data)

        status_byte = unpacked[0]
        time_stamp = unpacked[1]
        az_raw = unpacked[2]
        el_raw = unpacked[3]
        az_lag = unpacked[4]
        el_lag = unpacked[5]
        az_offset = unpacked[6]
        el_offset = unpacked[7]
        snr = unpacked[8]

        # 转换角度
        az_degrees = self.w6e_to_degrees(az_raw)
        el_degrees = self.w6e_to_degrees(el_raw)

        # 转换滞后值和脱靶值（从0.001度单位转换为度）
        az_lag_degrees = az_lag / 1000.0
        el_lag_degrees = el_lag / 1000.0
        az_offset_degrees = az_offset / 1000.0
        el_offset_degrees = el_offset / 1000.0

        return {
            'status': '自跟踪' if is_tracking else '非自跟踪',
            'time': time_stamp,
            'az_raw': az_raw,
            'el_raw': el_raw,
            'az_degrees': az_degrees,
            'el_degrees': el_degrees,
            'az_lag': az_lag_degrees,
            'el_lag': el_lag_degrees,
            'az_offset': az_offset_degrees,
            'el_offset': el_offset_degrees,
            'snr': snr
        }
    
    def receive_data(self, timeout=60):
        """
        接收并解析W6E数据
        
        Args:
            timeout: 接收超时时间（秒）
        """
        if not self.setup_socket():
            return
        
        self.running = True
        start_time = time.time()
        packet_count = 0
        
        print("开始接收W6E数据...")
        print("=" * 120)
        print(f"{'序号':<4} {'接收时间':<12} {'积秒时间':<12} {'状态':<8} {'方位角':<10} {'俯仰角':<10} {'信噪比':<8} {'AZ滞后':<8} {'EL滞后':<8}")
        print("-" * 120)
        
        try:
            self.socket.settimeout(1.0)  # 设置1秒超时
            
            while self.running and (time.time() - start_time) < timeout:
                try:
                    # 接收数据
                    data, addr = self.socket.recvfrom(1024)
                    packet_count += 1
                    
                    # 检查数据长度
                    if len(data) < 47:  # 24字节包头 + 23字节数据
                        print(f"数据包长度不足: {len(data)} 字节")
                        continue
                    
                    # 解析EMBL包头
                    header = self.parse_embl_header(data[:24])
                    if not header:
                        print("解析包头失败")
                        continue
                    
                    # 验证BID
                    if header['BID'] != 0x00100605:
                        print(f"BID不匹配: 0x{header['BID']:08X}")
                        continue
                    
                    # 解析W6E数据
                    w6e_data = self.parse_w6e_data(data[24:47])
                    if not w6e_data:
                        print("解析W6E数据失败")
                        continue
                    
                    # 转换时间
                    hour, minute, second, ms = self.js_to_hms(w6e_data['time'])
                    time_str = f"{hour:02d}:{minute:02d}:{second:02d}.{ms:04.0f}"
                    
                    # 获取当前接收时间
                    recv_time = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                    
                    # 显示解析结果
                    print(f"{packet_count:<4} {recv_time:<12} {time_str:<12} "
                          f"{w6e_data['status']:<8} {w6e_data['az_degrees']:<10.3f} "
                          f"{w6e_data['el_degrees']:<10.3f} {w6e_data['snr']:<8} "
                          f"{w6e_data['az_lag']:<8.3f} {w6e_data['el_lag']:<8.3f}")

                    # 详细信息（可选）
                    if packet_count <= 3:  # 只显示前3个包的详细信息
                        print(f"    详细信息:")
                        print(f"      包头: MID=0x{header['MID']:08X}, BID=0x{header['BID']:08X}, LEN={header['LEN']}")
                        print(f"      原始值: AZ_Raw=0x{w6e_data['az_raw']:08X}, EL_Raw=0x{w6e_data['el_raw']:08X}")
                        print(f"      滞后值: AZ={w6e_data['az_lag']:.3f}°, EL={w6e_data['el_lag']:.3f}°")
                        print(f"      脱靶值: AZ={w6e_data['az_offset']:.3f}°, EL={w6e_data['el_offset']:.3f}°")
                        print(f"      信噪比: {w6e_data['snr']}dB")
                        print(f"      来源: {addr[0]}:{addr[1]}")
                        print()
                    
                except socket.timeout:
                    # 超时继续循环
                    continue
                except Exception as e:
                    print(f"接收数据时出错: {e}")
                    break
                    
        except KeyboardInterrupt:
            print("\n收到中断信号，停止接收...")
        finally:
            self.stop()
            
        print(f"\n接收完成，共收到 {packet_count} 个数据包")
    
    def stop(self):
        """停止接收器"""
        self.running = False
        if self.socket:
            try:
                # 离开组播组
                mreq = struct.pack("4sl", socket.inet_aton(self.multicast_ip), socket.INADDR_ANY)
                self.socket.setsockopt(socket.IPPROTO_IP, socket.IP_DROP_MEMBERSHIP, mreq)
            except:
                pass
            self.socket.close()
            self.socket = None
        print("W6E数据接收器已停止")

def main():
    """主函数"""
    print("W6E数据接收测试器")
    print("=" * 50)
    
    # 可以根据需要修改这些参数
    multicast_ip = "***************"  # 组播地址
    port = 8080                       # 端口号
    
    # 创建接收器实例
    receiver = W6EReceiver(multicast_ip, port)
    
    try:
        # 开始接收数据，超时60秒
        receiver.receive_data(timeout=60)
    except Exception as e:
        print(f"运行出错: {e}")
    
    print("测试完成")

if __name__ == "__main__":
    main()
