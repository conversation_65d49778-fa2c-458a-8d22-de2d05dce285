﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Runtime.InteropServices;
using System.Runtime.Serialization.Formatters.Binary;
using System.Xml;
using CrossLeading.NetOld;

namespace CrossLeading
{
    /// <summary>
    /// EMBL数据帧头格式
    /// </summary>
    public struct EMBLHead
    {
        /// <summary>
        /// 积秒
        /// </summary>
        public uint Time;
        /// <summary>
        /// 任务标识
        /// </summary>
        public uint MID;       
        /// <summary>
        /// 信息类别
        /// </summary>
        public uint BID;        
        /// <summary>
        /// 保留字段1
        /// </summary>
        public uint Res1;
        /// <summary>
        /// 保留字段2
        /// </summary>
        public uint Res2;
        /// <summary>
        /// 数据字段长度
        /// </summary>
        public uint LEN;
    }

    /// <summary>
    /// EMBL数据帧头格式
    /// </summary>
    public struct EMBLHeadRadar
    {
        /// <summary>
        /// 信息类别
        /// </summary>
        public uint BID; 
        /// <summary>
        /// 积秒
        /// </summary>
        public ushort wDay;
        /// <summary>
        /// 任务代号
        /// </summary>
        public uint time;
        /// <summary>
        /// 保留字段1
        /// </summary>
        public byte[] Res;
        /// <summary>
        /// 数据字段长度
        /// </summary>
        public ushort LEN;
    }
    /// <summary>
    /// 测角数据
    /// </summary>
    public struct W6EData
    {
        /// <summary>
        /// 状态：自跟踪/非自跟踪
        /// </summary>
        public string ZT;
        /// <summary>
        /// 时标
        /// </summary>
        public uint Time;
        /// <summary>
        /// 方位（度）
        /// </summary>
        public double AZ;
        /// <summary>
        /// 俯仰（度）
        /// </summary>
        public double EL;
    }
    public struct W3CData
    {
        /// <summary>
        /// 状态：自跟踪/非自跟踪
        /// </summary>
        public string ZT;
        /// <summary>
        /// 时标
        /// </summary>
        public uint Time;
        /// <summary>
        /// 方位（度）
        /// </summary>
        public double AZ;
        /// <summary>
        /// 俯仰（度）
        /// </summary>
        public double EL;
    }
    public partial class FormMain : UserControl
    {
        /// <summary>
        /// 是否正在互引导
        /// </summary>
        bool isRunning = false;

        /// <summary>
        /// 引导天线名称
        /// </summary>
        string strAnntIn;
        /// <summary>
        /// 被引导天线名称列表
        /// </summary>
        List<string> strAnntOutList;

        /// <summary>
        /// 时统时间
        /// </summary>
        uint timeBJ;


        /// <summary>
        /// 正在使用QD引导
        /// </summary>
        bool usingQD = false;

        /// <summary>
        /// 打算向QD发送引导
        /// </summary>
        bool sendingQD = false;

        /// <summary>
        /// QD波束号
        /// </summary>
        uint qdSendUacNumber;
        uint qdRcvUacNumber;

        /// <summary>
        /// QD波束1-16的UAC地址（转为10进制后）
        /// </summary>
        uint[] qdDigUACList = {
                                1711684624, // 波束1
                                1711684632, // 波束2
                                1711684640, // 波束3
                                1711684648, // 波束4
                                1711684656, // 波束5
                                1711684664, // 波束6
                                1711684672, // 波束7
                                1711684680, // 波束8
                                1711684688, // 波束9
                                1711684696, // 波束10
                                1711684704, // 波束11
                                1711684712, // 波束12
                                1711684720, // 波束13
                                1711684728, // 波束14
                                1711684736, // 波束15
                                1711684744, // 波束16
                            };
        /// <summary>
        /// QD波束1-16的UAC地址
        /// </summary>
        uint[] qdHexUACList = {
                                0x66063C10, // 波束1
                                0x66063C18, // 波束2
                                0x66063C20, // 波束3
                                0x66063C28, // 波束4
                                0x66063C30, // 波束5
                                0x66063C38, // 波束6
                                0x66063C40, // 波束7
                                0x66063C48, // 波束8
                                0x66063C50, // 波束9
                                0x66063C58, // 波束10
                                0x66063C60, // 波束11
                                0x66063C68, // 波束12
                                0x66063C70, // 波束13
                                0x66063C78, // 波束14
                                0x66063C80, // 波束15
                                0x66063C88, // 波束16
                            };
        


        double[] SourceBLH = { 46.747872, 130.290212, 128.811 };
        NetConfig NetTS05 = new NetConfig();
        NetConfig NetQD = new NetConfig();
        NetConfig NetTS06 = new NetConfig();
        NetConfig NetTK23 = new NetConfig();
        NetRecvManager NetRcvTS05;
        NetRecvManager NetRcvQD;
        NetRecvManager NetRcvTS06;
        NetRecvManager NetRcvTK23;
        GroupNetSendThread NetSendTS05;
        GroupNetSendThread NetSendQD;
        GroupNetSendThread NetSendTS06;
        GroupNetSendThread NetSendTK23;

        uint midPer;
        #region 数据格式转换
        /// <summary>
        /// EMBL包头格式转换
        /// </summary>
        /// <param name="byteArr"></param>
        /// <returns></returns>
        public EMBLHead FormateHead(byte[] byteArr)
        {
            EMBLHead EMBLHeader = new EMBLHead();
            IntPtr buffer = Marshal.AllocCoTaskMem(24);
            Marshal.Copy(byteArr, 0, buffer, 24);
            EMBLHeader.Time = (uint)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 0), typeof(uint));
            EMBLHeader.MID = (uint)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 4), typeof(uint));
            EMBLHeader.BID = (uint)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 8), typeof(uint));
            EMBLHeader.Res1 = (uint)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 12), typeof(uint));
            EMBLHeader.Res2 = (uint)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 16), typeof(uint));
            EMBLHeader.LEN = (uint)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 20), typeof(uint));

            Marshal.FreeHGlobal(buffer);

            return EMBLHeader;
        }

        public W6EData FormtW6E(byte[] buff)
        {
            W6EData W6Edata = new W6EData();
            IntPtr buffer = Marshal.AllocCoTaskMem(23);
            Marshal.Copy(buff, 24, buffer, 23);
            byte br, zt;           
            zt = (byte)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 0), typeof(byte));            
            br = (byte)(zt & 0x02);
            if (br == 0x02)
                W6Edata.ZT = "自跟踪";
            else
                W6Edata.ZT = "非自跟踪";
            W6Edata.Time = (uint)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 1), typeof(uint));
            uint tmp = (uint)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 5), typeof(uint));
            W6Edata.AZ = tmp / Math.Pow(2, 32) * 360;
            tmp = (uint)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 9), typeof(uint));
            W6Edata.EL = tmp / Math.Pow(2, 32) * 360;

            return W6Edata;
        }
        public W3CData FormtW3C(byte[] buff)    //213数据
        {
            W3CData W3Cdata = new W3CData();
            IntPtr buffer = Marshal.AllocCoTaskMem(167);
            Marshal.Copy(buff, 0, buffer, 167);
            W3Cdata.Time = (uint)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 6), typeof(uint));

            byte workmode = (byte)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 28), typeof(byte));
            if (workmode == 0x02) //増程模式
            {
                double tmp = (double)Marshal.PtrToStructure(new IntPtr(buffer.ToInt64() + 133), typeof(double));
                W3Cdata.AZ = tmp;
                tmp = (double)Marshal.PtrToStructure(new IntPtr(buffer.ToInt64() + 141), typeof(double));
                W3Cdata.EL = tmp;

            }
            else //正常模式
            {
                float tmp = (float)Marshal.PtrToStructure(new IntPtr(buffer.ToInt64() + 40), typeof(float));
                W3Cdata.AZ = tmp;
                tmp = (float)Marshal.PtrToStructure(new IntPtr(buffer.ToInt64() + 44), typeof(float));
                W3Cdata.EL = tmp; 
            }

            return W3Cdata;
        }
        #endregion
        #region 时间转换函数
        /// <summary>
        /// 积秒-->时分秒毫秒
        /// </summary>
        /// <param name="dJS">积秒（0.1ms)</param>
        /// <param name="nHour">时</param>
        /// <param name="nMinute">分</param>
        /// <param name="nSecond">秒</param>
        /// <param name="dMS">毫秒(小数)</param>
        /// <returns>返回0:计算正常</returns>
        public int JS2HMS(uint dJS, out int nHour, out int nMinute, out int nSecond, out double dMS)
        {
            if (dJS < 0)
            {
                nHour = 0;
                nMinute = 0;
                nSecond = 0;
                dMS = 0.0;
                return -1;
            }

            double dTemp = dJS / 10;
            int nTemp = (int)dTemp;
            nHour = nTemp / 3600000;
            nMinute = (nTemp % 3600000) / 60000;
            nSecond = (nTemp % 60000) / 1000;
            dMS = dJS % 10000 / 10;
            return 0;
        }
        /// <summary>
        /// 时分秒毫秒-->积秒
        /// 功能：根据时、分、秒、毫秒计算当日积秒，并输出
        /// 输出：积秒，以0.1毫秒为单位
        /// </summary>
        /// <param name="nHour"></param>
        /// <param name="nMinute"></param>
        /// <param name="nSecond"></param>
        /// <param name="dMS"></param>
        /// <returns></returns>
        public uint GetJS(int nHour, int nMinute, int nSecond, double dMS)
        {
            uint uResult = 0;
            uResult = (uint)((((nHour * 60 + nMinute) * 60 + nSecond) * 1000 + dMS) * 10);
            return uResult;
        }
        /// <summary>
        /// 积日-->年月日
        /// </summary>
        /// <param name="nJD">积日,相对2000.0.0</param>
        /// <param name="nYear">年</param>
        /// <param name="nMonth">月</param>
        /// <param name="nDay">日</param>
        public void JD2YMD(int nJD, out int nYear, out int nMonth, out int nDay)
        {
            int l, m, n, i, j;
            l = nJD + 18294 + 18262;
            i = (int)Math.Floor((l * 4 - 365.0) / 1461.0);
            j = (int)(l - Math.Floor(i * 365.251));
            if (j < 92)
            {
                i = i - 1; j = j + 365;
            }
            m = (int)Math.Floor((j * 5 + 1.5) / 153.0);
            n = (int)(j - Math.Floor((m * 153.0 - 1.5) / 5.0));
            if (m > 12)
            {
                m = m - 12; i = i + 1;
            }
            nYear = 1900 + i;
            nMonth = m;
            nDay = n;
        }
        /// <summary>
        /// 年月日-->积日
        /// 功能：根据年、月、日计算积日，并输出
        ///	输入：b2000 = TRUE: 相对于2000年标志，否则为对应于1950
        /// </summary>
        /// <param name="nYear"></param>
        /// <param name="nMonth"></param>
        /// <param name="nDay"></param>
        /// <param name="b2000"></param>
        /// <returns></returns>
        public int GetJD(int nYear, int nMonth, int nDay, bool b2000)
        {
            int nResult = 0;
            if (nYear < 1950)
                return 0;
            if (nMonth < 3)
            {
                nMonth += 12;
                nYear--;
            }
            nYear = (int)((nYear - 1900) * 365.25);
            nMonth = (int)((nMonth * 153.0 - 1.5) / 5);
            nResult = nYear + nMonth + nDay;

            if (b2000)
                return (nResult - 18294 - 18262);
            else
                return (nResult - 18294);
        }

        #endregion

        delegate void SetTextCallback(TextBox textbx, string text);
        /// <summary>
        /// 以安全方式设置文本控件
        /// </summary>
        /// <param name="textbx"></param>
        /// <param name="str"></param>
        private void SetText(TextBox textbx, string str)
        {
            if (textbx.InvokeRequired)
            {
                SetTextCallback d = new SetTextCallback(SetText);
                try
                {
                    this.Invoke(d, new object[] { textbx, str });
                }
                catch
                {
                    return;
                }
            }
            else
            {
                textbx.Text = str;
            }
        }
        public FormMain()
        {
            InitializeComponent();
            // 加载配置文件
            LoadXML();
            
        }

        private void FormMain_Load(object sender, EventArgs e)
        {
            strAnntOutList = new List<string>();
            tbTime.Text = DateTime.Now.ToString("HH:mm:ss.ffff");
            timer1.Enabled = true;
            timer2.Enabled = true;
            // tbMid.Text = "NULL";
            tbAZin.Text = "NULL";
            tbELin.Text = "NULL";
            tbAZout.Text = "NULL";
            tbELout.Text = "NULL";

            strAnntOutList.Add("TK-4423");

            SourceBLH[0] = NetTS05.Lat;
            SourceBLH[1] = NetTS05.Lon;
            SourceBLH[2] = NetTS05.Hght;
            NetRcvTS05 = new NetRecvManager(RecvNetInfo, NetTS05.strGroupAddrRcv, NetTS05.strIpAddrRcv, NetTS05.iPortRcv);
            //NetRcvTS05.RunThread();
            NetRcvQD = new NetRecvManager(RecvNetInfo, NetQD.strGroupAddrRcv, NetQD.strIpAddrRcv, NetQD.iPortRcv);
            NetRcvTS06 = new NetRecvManager(RecvNetInfo, NetTS06.strGroupAddrRcv, NetTS06.strIpAddrRcv, NetTS06.iPortRcv);
            NetRcvTK23 = new NetRecvManager(RecvNetInfo, NetTK23.strGroupAddrRcv, NetTK23.strIpAddrRcv, NetTK23.iPortRcv);

            NetSendTS05 = new GroupNetSendThread(NetTS05.strGroupAddrSend, NetTS05.strIpAddrSend, NetTS05.iPortSend);
            NetSendQD = new GroupNetSendThread(NetQD.strGroupAddrSend, NetQD.strIpAddrSend, NetQD.iPortSend);
            NetSendTS06 = new GroupNetSendThread(NetTS06.strGroupAddrSend, NetTS06.strIpAddrSend, NetTS06.iPortSend);
            NetSendTK23 = new GroupNetSendThread(NetTK23.strGroupAddrSend, NetTK23.strIpAddrSend, NetTK23.iPortSend);
        }

        private void LoadXML()
        {
            // 加载配置文件
            string xmlName = System.Windows.Forms.Application.StartupPath + "\\Config.xml";
            //初始化数据集合
            if (!System.IO.File.Exists(xmlName))
            {
                throw new Exception(string.Format("{0}", "路径不存在"));
            }
            XmlDocument doc = new XmlDocument();
            doc.Load(xmlName);
            XmlElement root = doc.DocumentElement;
            XmlNode node;

            // TS-4205
            node = root.SelectSingleNode("TS-4205//RecvGroup");
            NetTS05.strGroupAddrRcv = node.SelectSingleNode("GroupAddress").InnerText;
            NetTS05.strIpAddrRcv = node.SelectSingleNode("IpAddress").InnerText;
            NetTS05.iPortRcv = int.Parse(node.SelectSingleNode("Port").InnerText);

            node = root.SelectSingleNode("TS-4205//SendGroup");
            NetTS05.strGroupAddrSend = node.SelectSingleNode("GroupAddress").InnerText;
            NetTS05.strIpAddrSend = node.SelectSingleNode("IpAddress").InnerText;
            NetTS05.iPortSend = int.Parse(node.SelectSingleNode("Port").InnerText);

            node = root.SelectSingleNode("TS-4205//Station");
            NetTS05.Lat = double.Parse(node.SelectSingleNode("B").InnerText);
            NetTS05.Lon = double.Parse(node.SelectSingleNode("L").InnerText);
            NetTS05.Hght = double.Parse(node.SelectSingleNode("H").InnerText);

            // QD-4203
            node = root.SelectSingleNode("QD-4203//RecvGroup");
            NetQD.strGroupAddrRcv = node.SelectSingleNode("GroupAddress").InnerText;
            NetQD.strIpAddrRcv = node.SelectSingleNode("IpAddress").InnerText;
            NetQD.iPortRcv = int.Parse(node.SelectSingleNode("Port").InnerText);

            node = root.SelectSingleNode("QD-4203//SendGroup");
            NetQD.strGroupAddrSend = node.SelectSingleNode("GroupAddress").InnerText;
            NetQD.strIpAddrSend = node.SelectSingleNode("IpAddress").InnerText;
            NetQD.iPortSend = int.Parse(node.SelectSingleNode("Port").InnerText);

            node = root.SelectSingleNode("QD-4203//Station");
            NetQD.Lat = double.Parse(node.SelectSingleNode("B").InnerText);
            NetQD.Lon = double.Parse(node.SelectSingleNode("L").InnerText);
            NetQD.Hght = double.Parse(node.SelectSingleNode("H").InnerText);

            // TS-4206
            node = root.SelectSingleNode("TS-4206//RecvGroup");
            NetTS06.strGroupAddrRcv = node.SelectSingleNode("GroupAddress").InnerText;
            NetTS06.strIpAddrRcv = node.SelectSingleNode("IpAddress").InnerText;
            NetTS06.iPortRcv = int.Parse(node.SelectSingleNode("Port").InnerText);

            node = root.SelectSingleNode("TS-4206//SendGroup");
            NetTS06.strGroupAddrSend = node.SelectSingleNode("GroupAddress").InnerText;
            NetTS06.strIpAddrSend = node.SelectSingleNode("IpAddress").InnerText;
            NetTS06.iPortSend = int.Parse(node.SelectSingleNode("Port").InnerText);

            node = root.SelectSingleNode("TS-4206//Station");
            NetTS06.Lat = double.Parse(node.SelectSingleNode("B").InnerText);
            NetTS06.Lon = double.Parse(node.SelectSingleNode("L").InnerText);
            NetTS06.Hght = double.Parse(node.SelectSingleNode("H").InnerText);

            // TK-4423
            node = root.SelectSingleNode("TK-4423//RecvGroup");
            NetTK23.strGroupAddrRcv = node.SelectSingleNode("GroupAddress").InnerText;
            NetTK23.strIpAddrRcv = node.SelectSingleNode("IpAddress").InnerText;
            NetTK23.iPortRcv = int.Parse(node.SelectSingleNode("Port").InnerText);

            node = root.SelectSingleNode("TK-4423//SendGroup");
            NetTK23.strGroupAddrSend = node.SelectSingleNode("GroupAddress").InnerText;
            NetTK23.strIpAddrSend = node.SelectSingleNode("IpAddress").InnerText;
            NetTK23.iPortSend = int.Parse(node.SelectSingleNode("Port").InnerText);

            node = root.SelectSingleNode("TK-4423//Station");
            NetTK23.Lat = double.Parse(node.SelectSingleNode("B").InnerText);
            NetTK23.Lon = double.Parse(node.SelectSingleNode("L").InnerText);
            NetTK23.Hght = double.Parse(node.SelectSingleNode("H").InnerText);
        }
        private void bnStart_Click(object sender, EventArgs e)
        {
            if (isRunning == false)
            {
                
                string message = "请选择至少一个被引导天线。";
			    string caption = "提示";
			    MessageBoxButtons buttons = MessageBoxButtons.YesNo;
                if (strAnntOutList.Count == 0)
                {
                    MessageBox.Show(this, message, caption);
                    return;
                }
                message = "将要向指定天线发送数引，确定要发送吗？";
                DialogResult result = MessageBox.Show(this, message, caption, buttons);
                //DialogResult result = MessageBox.Show(this, message, caption, buttons,
                //                        MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1);
                if (result != DialogResult.Yes)
                    return;
                midPer = uint.Parse(tbMidPer.Text.Trim(), System.Globalization.NumberStyles.HexNumber);
                timer3.Interval = 200;
                timer3.Start();

                //CrossLeading.Net.GroupNetSendThread.GetInstance();
                foreach (string AnntOut in strAnntOutList)
                {
                    switch (AnntOut)
                    {
                        case "TS-4206":
                            NetSendTS06.RunThread();
                            break;
                        case "QD-4203":
                            NetSendQD.RunThread();
                            break;
                        case "TS-4205":
                            NetSendTS05.RunThread();
                            break;
                        case "TK-4423":
                            NetSendTK23.RunThread();
                            break;
                    }
                }


                isRunning = true;
                grpAntIn.Enabled = false;
                grpAntOut.Enabled = false;
                grpPerSet.Enabled = false;
                bnStart.Text = "停止";
            }
            else
            {
                isRunning = false;
                grpAntIn.Enabled = true;
                grpAntOut.Enabled = true;
                grpPerSet.Enabled = true;
                bnStart.Text = "开始";
                timer3.Stop();

                NetSendTS06.AbortThread();
                NetSendQD.AbortThread();
                NetSendTS05.AbortThread();
                NetSendTK23.AbortThread();
            }
        }
        /// <summary>
        /// 更新本地时间显示
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void timer1_Tick(object sender, EventArgs e)
        {
            tbTime.Text = DateTime.Now.ToString("HH:mm:ss.ffff");
        }
        /// <summary>
        /// 更新数据显示窗口，当无数据时清空
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void timer2_Tick(object sender, EventArgs e)
        {
            //SetText(tbTimeBJ, "NULL");
            //tbMid.Text = "NULL";
            tbAZin.Text = "NULL";
            tbELin.Text = "NULL";
            tbAZout.Text = "NULL";
            tbELout.Text = "NULL";
        }

        private void BlinkTimer_Tick(object sender, EventArgs e)
        {
            if(isRunning)
            {
                foreach(Control c in grpAntIn.Controls)
                {
                    if(c is RadioButton)
                    {
                        RadioButton rb = c as RadioButton;
                        if (rb.Checked)
                        {
                            if (rb.ForeColor == Color.Black)
                                rb.ForeColor = Color.Red;
                            else
                                rb.ForeColor = Color.Black;
                        }
                    }
                    if (c is CheckBox)
                    {
                        CheckBox cb = c as CheckBox;
                        if (cb.Checked)
                        {
                            if (cb.ForeColor == Color.Black)
                                cb.ForeColor = Color.Red;
                            else
                                cb.ForeColor = Color.Black;
                        }
                    }
                }
            }
        }
        /// <summary>
        /// TODO: 佳木斯没有雷达设备，没什么用
        /// </summary>
        /// <param name="byteArr"></param>
        public void RecvNetLD(byte[] byteArr)
        {
            timer2.Stop();
            //W3CData W3Cdata = FormtW3C(byteArr);
            CrossLeading.Net.FrameHBL radar = CrossLeading.Net.FrameHBL.Parse(byteArr,0);

            int nHour, nMinute, nSecond;
            double dMS;

            timeBJ = (uint)(radar._tarData[0].time_h) * (uint)(3600); //+ radar._tarData[0].time_m * 60 + radar._tarData[0].time_s;
            timeBJ += (uint)(radar._tarData[0].time_m) * (uint)(60);
            timeBJ += (uint)(radar._tarData[0].time_s) * (uint)(1);
            timeBJ *= 1000;
            timeBJ += (uint)(radar._tarData[0].nb_50ms) * (uint)(50);
            timeBJ *= 10;

            JS2HMS(timeBJ, out nHour, out nMinute, out nSecond, out dMS);
            string tm = string.Format("{0:00}:{1:00}:{2:00}.{3:0000}", nHour, nMinute, nSecond, dMS * 10);
            SetText(tbMid, tm);
            SetText(tbAZin, radar._tarData[0].a.ToString("0.000"));
            SetText(tbELin, radar._tarData[0].e.ToString("0.000"));

            //////////////////////////////////////////////////////////////////////////
            // 坐标变换(1) 产生XYZ            
            double Range = Convert.ToDouble(tbRagPer.Text) * 1000;
            double[] RAE = new double[6];
            RAE[0] = Range;
            RAE[1] = radar._tarData[0].a;
            RAE[2] = radar._tarData[0].e;
            double[] staXYZ = OrbitForcast.RAE2staRxyz(RAE);
            double[] Gxyz = OrbitForcast.staRxyz2Gxyz(staXYZ, SourceBLH);
            //////////////////////////////////////////////////////////////////////////
            // 坐标变换(2) 显示反向变换后的角度
            double[] staXYZ2;
            double[] RAE2;

            double[] TargetBLH = { 0, 0, 0 };
            if (strAnntOutList.Count == 0)
            {
                SetText(tbAZout, "NULL");
                SetText(tbELout, "NULL");
            }
            else
            {
                switch (strAnntOutList[0])
                {
                    case "TS-4206":
                        TargetBLH[0] = NetTS06.Lat;
                        TargetBLH[1] = NetTS06.Lon;
                        TargetBLH[2] = NetTS06.Hght;
                        break;
                    case "QD-4203":
                        TargetBLH[0] = NetQD.Lat;
                        TargetBLH[1] = NetQD.Lon;
                        TargetBLH[2] = NetQD.Hght;
                        break;
                    case "TS-4205":
                        TargetBLH[0] = NetTS05.Lat;
                        TargetBLH[1] = NetTS05.Lon;
                        TargetBLH[2] = NetTS05.Hght;
                        break;
                }
                staXYZ2 = OrbitForcast.Gxyz2staRxyz(Gxyz, TargetBLH);
                RAE2 = OrbitForcast.staRxyz2RAE(staXYZ2);
                SetText(tbAZout, RAE2[1].ToString("0.000"));
                SetText(tbELout, RAE2[2].ToString("0.000"));
            }
            //////////////////////////////////////////////////////////////////////////
            // 转发引导
            if (isRunning == true)
                SendP12Data(timeBJ, midPer, Gxyz);
            //////////////////////////////////////////////////////////////////////////
            // 重启计时器2
            timer2.Start();
        }


        public void RecvNetInfo(byte[] byteArr)
        {

#region 增加雷达引导部分代码 20160331


            EMBLHeadRadar EMBLHeader = new EMBLHeadRadar();
            IntPtr buffer = Marshal.AllocCoTaskMem(20);
            Marshal.Copy(byteArr, 0, buffer, 20);

            EMBLHeader.BID = (uint)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 0), typeof(uint));

            Marshal.FreeHGlobal(buffer);

            if (EMBLHeader.BID == 1313)
            {
                timer2.Stop();
                W3CData W3Cdata = FormtW3C(byteArr);
                //FrameHBL radar = FrameHBL.Parse(byteArr,0);

                int nHour, nMinute, nSecond;
                double dMS;

                timeBJ = W3Cdata.Time*100;

                JS2HMS(timeBJ, out nHour, out nMinute, out nSecond, out dMS);
                string tm = string.Format("{0:00}:{1:00}:{2:00}.{3:0000}", nHour, nMinute, nSecond, dMS * 10);
                SetText(tbMid, tm);
                SetText(tbAZin, W3Cdata.AZ.ToString("0.000"));
                SetText(tbELin, W3Cdata.EL.ToString("0.000"));

                //////////////////////////////////////////////////////////////////////////
                // 坐标变换(1) 产生XYZ            
                double Range = Convert.ToDouble(tbRagPer.Text) * 1000;
                double[] RAE = new double[6];
                RAE[0] = Range;
                RAE[1] = W3Cdata.AZ;
                RAE[2] = W3Cdata.EL;
                double[] staXYZ = OrbitForcast.RAE2staRxyz(RAE);
                double[] Gxyz = OrbitForcast.staRxyz2Gxyz(staXYZ, SourceBLH);
                //////////////////////////////////////////////////////////////////////////
                // 坐标变换(2) 显示反向变换后的角度
                double[] staXYZ2;
                double[] RAE2;

                double[] TargetBLH = { 0, 0, 0 };
                if (strAnntOutList.Count == 0)
                {
                    SetText(tbAZout, "NULL");
                    SetText(tbELout, "NULL");
                }
                else
                {
                    switch (strAnntOutList[0])
                    {
                        case "TS-4206":
                            TargetBLH[0] = NetTS06.Lat;
                            TargetBLH[1] = NetTS06.Lon;
                            TargetBLH[2] = NetTS06.Hght;
                            break;
                        case "QD-4203":
                            TargetBLH[0] = NetQD.Lat;
                            TargetBLH[1] = NetQD.Lon;
                            TargetBLH[2] = NetQD.Hght;
                            break;
                        case "TS-4205":
                            TargetBLH[0] = NetTS05.Lat;
                            TargetBLH[1] = NetTS05.Lon;
                            TargetBLH[2] = NetTS05.Hght;
                            break;
                    }
                    staXYZ2 = OrbitForcast.Gxyz2staRxyz(Gxyz, TargetBLH);
                    RAE2 = OrbitForcast.staRxyz2RAE(staXYZ2);
                    SetText(tbAZout, RAE2[1].ToString("0.000"));
                    SetText(tbELout, RAE2[2].ToString("0.000"));
                }
                //////////////////////////////////////////////////////////////////////////
                // 转发引导
                if (isRunning == true)
                    SendP12Data(timeBJ, midPer, Gxyz);
                //////////////////////////////////////////////////////////////////////////
                // 重启计时器2
                timer2.Start();
            }
#endregion
            else
            {
                //////////////////////////////////////////////////////////////////////////
                // 转换EMBL包头
                EMBLHead emblHead = FormateHead(byteArr);

                int nHour, nMinute, nSecond;
                double dMS;
                //////////////////////////////////////////////////////////////////////////
                // 转换W6E数据，并显示
                if (emblHead.BID !=0x00100605  && emblHead.BID != 0x00007E00 )//0x00110111 0x000000f0
                {
                    return;
                }

                uint uacTmp = emblHead.Res1;
                if (usingQD && uacTmp != qdSendUacNumber)
                {
                    return;
                }

                timer2.Stop(); // 只要timer2停下来，就不会每两秒调用一次timer2_Tick，也就不会每两秒将窗口中数据设置为空了
                W6EData W6Edata = FormtW6E(byteArr);
                JS2HMS(W6Edata.Time, out nHour, out nMinute, out nSecond, out dMS);
                string tm = string.Format("{0:00}:{1:00}:{2:00}.{3:0000}", nHour, nMinute, nSecond, dMS * 10);
                SetText(tbMid, tm);
                SetText(tbAZin, W6Edata.AZ.ToString("0.000"));
                SetText(tbELin, W6Edata.EL.ToString("0.000"));
                timeBJ = W6Edata.Time;
                //////////////////////////////////////////////////////////////////////////
                // 坐标变换(1) 产生XYZ            
                double Range = Convert.ToDouble(tbRagPer.Text) * 1000;
                double[] RAE = new double[6];
                RAE[0] = Range;
                RAE[1] = W6Edata.AZ;
                RAE[2] = W6Edata.EL;
                double[] staXYZ = OrbitForcast.RAE2staRxyz(RAE);
                double[] Gxyz = OrbitForcast.staRxyz2Gxyz(staXYZ, SourceBLH);
                //////////////////////////////////////////////////////////////////////////
                // 坐标变换(2) 显示反向变换后的角度
                double[] staXYZ2;
                double[] RAE2;

                double[] TargetBLH = { 0, 0, 0 };
                if (strAnntOutList.Count == 0)
                {
                    SetText(tbAZout, "NULL");
                    SetText(tbELout, "NULL");
                }
                else
                {
                    switch (strAnntOutList[0])
                    {
                        case "TS-4206":
                            TargetBLH[0] = NetTS06.Lat;
                            TargetBLH[1] = NetTS06.Lon;
                            TargetBLH[2] = NetTS06.Hght;
                            break;
                        case "QD-4203":
                            TargetBLH[0] = NetQD.Lat;
                            TargetBLH[1] = NetQD.Lon;
                            TargetBLH[2] = NetQD.Hght;
                            break;
                        case "TS-4205":
                            TargetBLH[0] = NetTS05.Lat;
                            TargetBLH[1] = NetTS05.Lon;
                            TargetBLH[2] = NetTS05.Hght;
                            break;
                        case "TK-4423":
                            TargetBLH[0] = NetTK23.Lat;
                            TargetBLH[1] = NetTK23.Lon;
                            TargetBLH[2] = NetTK23.Hght;
                            break;
                    }
                    staXYZ2 = OrbitForcast.Gxyz2staRxyz(Gxyz, TargetBLH);
                    RAE2 = OrbitForcast.staRxyz2RAE(staXYZ2);
                    SetText(tbAZout, RAE2[1].ToString("0.000"));
                    SetText(tbELout, RAE2[2].ToString("0.000"));
                }
                //////////////////////////////////////////////////////////////////////////
                // 转发引导
                if (isRunning == true)
                    SendP12Data(W6Edata.Time, midPer, Gxyz);
                //////////////////////////////////////////////////////////////////////////
                // 重启计时器2
                timer2.Start();                      
            
            }
        }

        private void SendP12Data(uint BJtm, uint mid, double[] Gxyz)
        {
            byte[] Buff = new byte[24 + 28];
            IntPtr pnt = Marshal.AllocHGlobal(24 + 28);
            EMBLHead embl = new EMBLHead();
            embl.Time = BJtm;
            embl.MID = mid;
            embl.BID = 0x002A0160;
            embl.Res1 = 0x00;
            // 向qd发送引导数据，需要设置保留字段1为对应的UAC号
            if (sendingQD)
            {
                embl.Res1 = qdRcvUacNumber;
            }
            embl.Res2 = 0x00;
            embl.LEN = 28;
            int tmp;
            int offset = 24;
            try
            {
                Marshal.StructureToPtr(embl.Time, pnt, false);
                Marshal.Copy(pnt, Buff, 0, sizeof(uint));

                Marshal.StructureToPtr(embl.MID, pnt, false);
                Marshal.Copy(pnt, Buff, 4, sizeof(uint));

                Marshal.StructureToPtr(embl.BID, pnt, false);
                Marshal.Copy(pnt, Buff, 8, sizeof(uint));

                Marshal.StructureToPtr(embl.Res1, pnt, false);
                Marshal.Copy(pnt, Buff, 12, sizeof(uint));

                Marshal.StructureToPtr(embl.Res2, pnt, false);
                Marshal.Copy(pnt, Buff, 16, sizeof(uint));

                Marshal.StructureToPtr(embl.LEN, pnt, false);
                Marshal.Copy(pnt, Buff, 20, sizeof(uint));

                //
                Marshal.StructureToPtr(BJtm, pnt, false);
                Marshal.Copy(pnt, Buff, offset + 0, sizeof(uint));

                tmp = Convert.ToInt32(Gxyz[0] * 10);
                Marshal.StructureToPtr(tmp, pnt, false);
                Marshal.Copy(pnt, Buff, offset + 4, sizeof(int));

                tmp = Convert.ToInt32(Gxyz[1] * 10);
                Marshal.StructureToPtr(tmp, pnt, false);
                Marshal.Copy(pnt, Buff, offset + 8, sizeof(int));

                tmp = Convert.ToInt32(Gxyz[2] * 10);
                Marshal.StructureToPtr(tmp, pnt, false);
                Marshal.Copy(pnt, Buff, offset + 12, sizeof(int));

                tmp = Convert.ToInt32(Gxyz[3] * 100);
                Marshal.StructureToPtr(tmp, pnt, false);
                Marshal.Copy(pnt, Buff, offset + 16, 4);

                tmp = Convert.ToInt32(Gxyz[4] * 100);
                Marshal.StructureToPtr(tmp, pnt, false);
                Marshal.Copy(pnt, Buff, offset + 20, 4);

                tmp = Convert.ToInt32(Gxyz[5] * 100);
                Marshal.StructureToPtr(tmp, pnt, false);
                Marshal.Copy(pnt, Buff, offset + 24, 4);
            }
            finally
            {
                Marshal.FreeHGlobal(pnt);
            }
       
            foreach (string AnntOut in strAnntOutList)
            {
                switch (AnntOut)
                {
                    case "TS-4206":
                        NetSendTS06.SendlinkData(Buff);                       
                        break;
                    case "QD-4203":
                        NetSendQD.SendlinkData(Buff);
                        break;
                    case "TS-4205":
                        NetSendTS05.SendlinkData(Buff);
                        break;
                    case "TK-4423":
                        NetSendTK23.SendlinkData(Buff);
                        break;
                }
            }
            //GroupNetSendThread.GetInstance().SendlinkData(Buff);
        }
                             

        private void RBoxComm_CheckedChanged(object sender, EventArgs e)
        {
            RadioButton RadBt = (RadioButton)sender;
            try
            {
                switch (RadBt.Text)
                {
                    case "TS-4205":
                        if (RadBt.Checked == true)
                        {
                            usingQD = false;
                            coBoxQDSend.Enabled = false;
                            SourceBLH[0] = NetTS05.Lat;
                            SourceBLH[1] = NetTS05.Lon;
                            SourceBLH[2] = NetTS05.Hght;
                            NetRcvTS05.RunThread();
                        }
                        else
                            NetRcvTS05.AbortThread();
                        break;
                    case "QD-4203":
                        if (RadBt.Checked == true)
                        {
                            usingQD = true;
                            coBoxQDSend.Enabled = true;
                            SourceBLH[0] = NetQD.Lat;
                            SourceBLH[1] = NetQD.Lon;
                            SourceBLH[2] = NetQD.Hght;
                            NetRcvQD.RunThread();
                        }
                        else
                            NetRcvQD.AbortThread();
                        break;
                    case "TS-4206":
                        if (RadBt.Checked == true)
                        {
                            usingQD = false;
                            coBoxQDSend.Enabled = false;
                            SourceBLH[0] = NetTS06.Lat;
                            SourceBLH[1] = NetTS06.Lon;
                            SourceBLH[2] = NetTS06.Hght;
                            NetRcvTS06.RunThread();
                        }
                        else
                            NetRcvTS06.AbortThread();
                        break;
                    case "TK-4423":
                        if (RadBt.Checked == true)
                        {
                            usingQD = false;
                            coBoxQDSend.Enabled = false;
                            SourceBLH[0] = NetTK23.Lat;
                            SourceBLH[1] = NetTK23.Lon;
                            SourceBLH[2] = NetTK23.Hght;
                            NetRcvTK23.RunThread();
                        }
                        else
                            NetRcvTK23.AbortThread();
                        break;
                }
            }
            catch (Exception ex)
            {

            }

            if (RadBt.Checked == true)
            {
                strAnntIn = RadBt.Text;
                tbIn.Text = strAnntIn;
            }
        }

        private void CkBoxComm_CheckedChanged(object sender, EventArgs e)
        {
            CheckBox chkBt = (CheckBox)sender;

            strAnntOutList.Clear();
            if (CkBox4205.Checked == true)
                strAnntOutList.Add("TS-4205");
            if (CkBox4203.Checked == true)
            {
                strAnntOutList.Add("QD-4203");
                sendingQD = true;
                coBoxQDRcv.Enabled = true;
            }
            else
            {
                sendingQD = false;
                coBoxQDRcv.Enabled = false;
            }
            if (CkBox4206.Checked == true)
                strAnntOutList.Add("TS-4206");
            if (CkBox4423.Checked == true)
                strAnntOutList.Add("TK-4423");

            if (strAnntOutList.Count == 0)
                tbOut.Text = "";
            else if (strAnntOutList.Count == 1)
                tbOut.Text = string.Format("\n\t{0}\n\t", strAnntOutList[0]);
            else if (strAnntOutList.Count == 2)
                tbOut.Text = string.Format("{0}\n\t \n\t{1}", strAnntOutList[0], strAnntOutList[1]);
            else if (strAnntOutList.Count == 3)
                tbOut.Text = string.Format("{0}\n\t{1}\n\t{2}", strAnntOutList[0], strAnntOutList[1], strAnntOutList[2]);
            else if (strAnntOutList.Count == 4)
                tbOut.Text = string.Format("{0}\n\t{1}\n\t{2}\n\t{3}", strAnntOutList[0], strAnntOutList[1], strAnntOutList[2], strAnntOutList[3]);
        }

        private void label28_Click(object sender, EventArgs e)
        {

        }

        //////////////////////////////////////////////////////////////////////////
        // 选择用QD发送引导数据，设置相应的波束号（10进制）
        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            ComboBox cmbBox = (ComboBox)sender;
            // 10进制UAC值
            int uacIdx = cmbBox.SelectedIndex;
            qdSendUacNumber = qdDigUACList[uacIdx];
        }

        //////////////////////////////////////////////////////////////////////////
        // 选择用QD接收引导数据，设置相应的波束号（16进制）
        private void coBoxRcv_SelectedChanged(object sender, EventArgs e)
        {
            ComboBox cmbBox = (ComboBox)sender;
            // 16进制UAC值
            int uacIdx = cmbBox.SelectedIndex;
            qdRcvUacNumber = qdHexUACList[uacIdx];
        }

        private void tbDaihaoSetting_TextChanged(object sender, EventArgs e)
        {
            tbDaiHao.Text = (sender as TextBox).Text;
        }
    }
    /// <summary>
    /// 相对于引导系统，每个设备的收发地址配置
    /// </summary>
    public class NetConfig
    {
        public string strIpAddrRcv;
        public string strGroupAddrRcv;
        public int iPortRcv;

        public string strIpAddrSend;
        public string strGroupAddrSend;
        public int iPortSend;

        public double Lat;
        public double Lon;
        public double Hght;
       
    }
}
