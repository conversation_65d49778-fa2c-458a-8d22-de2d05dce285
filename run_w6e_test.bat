@echo off
chcp 65001 >nul
echo ========================================
echo W6E数据模拟器测试脚本
echo ========================================
echo.
echo 此脚本将启动W6E数据模拟器和接收器进行测试
echo 请确保Python已正确安装
echo.
echo 按任意键开始测试，或按Ctrl+C退出...
pause >nul

echo.
echo 正在启动W6E数据模拟器...
echo 模拟器将运行30秒，每20秒发送一次数据
echo.

start "W6E模拟器" cmd /k "python w6e_simulator.py && echo. && echo 模拟器已完成，按任意键关闭... && pause >nul"

echo.
echo 等待3秒后启动接收器...
timeout /t 3 /nobreak >nul

echo.
echo 正在启动W6E数据接收器...
echo 接收器将监听组播数据并显示解析结果
echo.

start "W6E接收器" cmd /k "python test_w6e_receiver.py"

echo.
echo 测试已启动！
echo.
echo 两个窗口已打开：
echo 1. W6E模拟器 - 发送模拟数据
echo 2. W6E接收器 - 接收并解析数据
echo.
echo 请观察两个窗口的输出结果
echo 模拟器完成后，可以手动关闭接收器窗口
echo.
echo 按任意键退出此脚本...
pause >nul
