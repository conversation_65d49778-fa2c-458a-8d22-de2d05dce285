﻿using CrossLeading.Cards;
using CrossLeading.Net;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml;

namespace MyListBox
{
    public partial class Form1 : Form
    {
        /// <summary>
        /// 存储设备配置信息的字典
        /// </summary>
        public static Dictionary<string, DevConf> devConfDict = new Dictionary<string, DevConf>();
        // public static CrossLeading.FormMain formMainCard = new CrossLeading.FormMain();
        public static CrossLeading.Cards.DeviceConfig deviceConfigCard = new CrossLeading.Cards.DeviceConfig();
        public static CrossLeading.Cards.NewUI newUI = new CrossLeading.Cards.NewUI();
        public static CrossLeading.Cards.VisibilityPredictCard vpCard = new CrossLeading.Cards.VisibilityPredictCard();
        public Form1()
        {
            InitializeComponent();
            try
            {
                LoadDevConf();
            }
            catch (Exception)
            {
                MessageBox.Show("请检查配置文件格式是否正确！");
                // throw;
            }
            
            // newUI.InitCbItems();
            // newUI.CreateNetManager();

            funcMenu1.Items.Add(new FuncItem("实时引导", "ui", CrossLeading.Properties.Resources.Rocket));
            funcMenu1.Items.Add(new FuncItem("设备配置", "deviceConfig", CrossLeading.Properties.Resources.Config));
            funcMenu1.Items.Add(new FuncItem("共视计算", "VisibilityPredictCard", CrossLeading.Properties.Resources.Satellite));
            //funcMenu1.Items.Add(new FuncItem("引导界面2", "deviceConfig", CrossLeading.Properties.Resources.Client));
            funcMenu1.SelectedIndex = 0;

            //afCardLayout1.AddCard(formMainCard, "formMainCard");
            afCardLayout1.AddCard(newUI, "newUI");
            afCardLayout1.AddCard(deviceConfigCard, "deviceConfigCard");
            afCardLayout1.AddCard(vpCard, "visibilityPredictCard");
            //afCardLayout1.AddCard(newUI, "newUI");
            afCardLayout1.ShowCard(0);
        }

        /// <summary>
        /// 从config.xml文件中加载设备配置信息
        /// </summary>
        public static void LoadDevConf()
        {
            // 加载配置文件
            string xmlName = System.Windows.Forms.Application.StartupPath + "\\Config.xml";
            //初始化数据集合
            if (!System.IO.File.Exists(xmlName))
            {
                throw new Exception(string.Format("{0}", "路径不存在"));
            }
            XmlDocument doc = new XmlDocument();
            doc.Load(xmlName);
            XmlElement root = doc.DocumentElement;
            XmlNode node;

            devConfDict.Clear();

            foreach (XmlNode devConfNode in root)
            {
                if (devConfNode.NodeType == XmlNodeType.Element)
                {
                    string devName = devConfNode.Name;
                    node = root.SelectSingleNode(devConfNode.Name + "//RecvGroup");
                    string strDataIpAddr = node.SelectSingleNode("IpAddress").InnerText;
                    string strRecvGCAddr = node.SelectSingleNode("GroupAddress").InnerText;
                    string strRecvGCPort = node.SelectSingleNode("Port").InnerText;

                    node = root.SelectSingleNode(devConfNode.Name + "//SendGroup");
                    string strSendGCAddr = node.SelectSingleNode("GroupAddress").InnerText;
                    string strSendGCPort = node.SelectSingleNode("Port").InnerText;

                    node = root.SelectSingleNode(devConfNode.Name + "//Station");
                    string strLati = node.SelectSingleNode("B").InnerText;
                    string strLongi = node.SelectSingleNode("L").InnerText;
                    string strHeight = node.SelectSingleNode("H").InnerText;

                    node = root.SelectSingleNode(devConfNode.Name + "//isQD");
                    bool isQD = node.InnerText == "是" ? true : false;

                    DevConf devConf = new DevConf();
                    devConf.devName = devName;
                    devConf.dataIPAddr = strDataIpAddr;
                    devConf.gpRecvAddr = strRecvGCAddr;
                    devConf.gpRecvPort = uint.Parse(strRecvGCPort);
                    devConf.gpSendAddr = strSendGCAddr;
                    devConf.gpSendPort = uint.Parse(strSendGCPort);
                    devConf.lati = double.Parse(strLati);
                    devConf.longi = double.Parse(strLongi);
                    devConf.height = double.Parse(strHeight);
                    devConf.isQD = isQD;
                    devConfDict[devName] = devConf;
                }
            }
        }

        private void funcMenu_SelectedIndexChanged(object sender, EventArgs e)
        {
            int index = funcMenu1.SelectedIndex;
            afCardLayout1.ShowCard(index);
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            newUI.InitCbItems();
        }
    }

    /// <summary>
    /// 标识一个设备的配置信息，包括收组播、发组播、地理坐标、是否为多波束设备
    /// </summary>
    public class DevConf
    {
        /// <summary>
        /// 设备代号
        /// </summary>
        public string devName;
        /// <summary>
        /// 互引导设备连接到此设备数据网的IP地址
        /// </summary>
        public string dataIPAddr;
        /// <summary>
        /// DTE收ACU组播地址
        /// </summary>
        public string gpRecvAddr;
        /// <summary>
        /// DTE收ACU组播端口
        /// </summary>
        public uint gpRecvPort; 
        /// <summary>
        /// DTE发ACU组播地址
        /// </summary>
        public string gpSendAddr; 
        /// <summary>
        /// DTE发ACU组播端口
        /// </summary>
        public uint gpSendPort;
        /// <summary>
        /// 纬度
        /// </summary>
        public double lati;
        /// <summary>
        /// 经度
        /// </summary>
        public double longi;
        /// <summary>
        /// 高程
        /// </summary>
        public double height;
        /// <summary>
        /// 是否为多波束装备
        /// </summary>
        public bool isQD;
    }
}
