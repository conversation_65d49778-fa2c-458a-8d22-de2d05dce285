﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using CrossLeading.Net;
using MyListBox;

namespace CrossLeading.Controls
{
    public partial class DeviceControl : UserControl
    {
        public bool isSource = true;
        public string devName;

        public DeviceControl()
        {
            InitializeComponent();
        }

        public DeviceControl(string devName)
        {
            InitializeComponent();
            this.devName = devName;
            this.lbDevName.Text = devName;
        }

        public string tbAZPer
        {
            get
            {
                return tbAZ.Text;
            }
            set
            {
                tbAZ.Text = value;
            }
        }
        public string tbELPer
        {
            get
            {
                return tbEL.Text;
            }
            set
            {
                tbEL.Text = value;
            }
        }

        /// <summary>
        /// 距离显示属性
        /// </summary>
        public string tbRangePer
        {
            get
            {
                return tbRange.Text;
            }
            set
            {
                tbRange.Text = value;
            }
        }

        public void setDevName(string name)
        {
            this.lbDevName.Text = name;
            this.devName = name;
        }

        /// <summary>
        /// 更新距离显示
        /// </summary>
        /// <param name="currentTime">当前时间</param>
        public void UpdateRangeDisplay(DateTime currentTime)
        {
            try
            {
                if (string.IsNullOrEmpty(devName))
                {
                    Form1.newUI.SetText(tbRange, "0.0km");
                    return;
                }

                // 获取插值后的距离值
                double range = RangeForecastManager.GetInterpolatedRange(devName, currentTime);

                if (range > 0)
                {
                    // 保留一位小数，单位km
                    Form1.newUI.SetText(tbRange, $"{range:F1}km");
                }
                else
                {
                    // 没有数据时显示默认值
                    Form1.newUI.SetText(tbRange, "0.0km");
                    tbRange.Text = "0.0km";
                }
            }
            catch (Exception)
            {
                // 异常情况下显示默认值
                Form1.newUI.SetText(tbRange, "0.0km");
            }
        }

        /// <summary>
        /// 清空距离显示
        /// </summary>
        public void ClearRangeDisplay()
        {
            tbRange.Text = "0.0km";
        }
    }
}
