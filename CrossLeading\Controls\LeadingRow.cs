﻿using CrossLeading.Net;
using MyListBox;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace CrossLeading.Controls
{
    public partial class LeadingRow : UserControl
    {
        public string sourceDevName;
        public string targetDevName;
        public string biaoshi;
        public string daihao;
        public double forcaseRange;
        public bool isRunning;
        /// <summary>
        /// 接收不到数据时，控制相应的LeadingRow内角度显示为NULL
        /// </summary>
        public Timer timerNull;

        public bool selected;

        public LeadingProcess leadingProcess;
        public LeadingRow()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="srcName">引导源设备</param>
        /// <param name="tarName">被引导设备</param>
        /// <param name="biaoshi">任务标识</param>
        /// <param name="daihao">任务代号</param>
        /// <param name="range">距离预报值</param>
        public LeadingRow(string srcName, string tarName, string biaoshi, string daihao, double range)
        {
            InitializeComponent();
            sourceDevName = srcName;
            targetDevName = tarName;
            ctlSrcDev.setDevName(srcName);
            if (srcName.StartsWith("QD"))
            {
                ctlSrcDev.devName = srcName.Substring(0, 7);
                sourceDevName = srcName.Substring(0, 7);
            }
            else
            {
                ctlSrcDev.devName = srcName;
            }
            ctlTarDev.setDevName(tarName);
            if (tarName.StartsWith("QD"))
            {
                ctlTarDev.devName = tarName.Substring(0, 7);
                targetDevName = tarName.Substring(0, 7);
            }
            else
            {
                ctlTarDev.devName = tarName;
            }
            this.biaoshi = biaoshi;
            this.daihao = daihao;
            tbBiaoshi.Text = biaoshi;
            tbDaihao.Text = daihao;
            forcaseRange = range;

            timerNull = new Timer();
            timerNull.Interval = 2000;
            timerNull.Tick += new System.EventHandler(timerNull_Tick);
        }

        public string srcDevName
        {
            get
            {
                return sourceDevName;
            }
            set
            {
                sourceDevName = value;
                ctlSrcDev.lbDevName.Text = value;
            }
        }

        public string srcDevAZ
        {
            get
            {
                return ctlSrcDev.tbAZPer;
            }
            set
            {
                ctlSrcDev.tbAZPer = value;
            }
        }

        public string srcDevEL
        {
            get
            {
                return ctlSrcDev.tbELPer;
            }
            set
            {
                ctlSrcDev.tbELPer = value;
            }
        }
        public string tarDevName
        {
            get
            {
                return targetDevName;
            }
            set
            {
                targetDevName = value;
                ctlTarDev.lbDevName.Text = value;
            }
        }

        public string tarDevAZ
        {
            get
            {
                return ctlTarDev.tbAZPer;
            }
            set
            {
                ctlTarDev.tbAZPer = value;
            }
        }


        public string tarDevEL
        {
            get
            {
                return ctlTarDev.tbELPer;
            }
            set
            {
                ctlTarDev.tbELPer = value;
            }
        }

        /// <summary>
        /// 引导源距离显示属性
        /// </summary>
        public string srcDevRange
        {
            get
            {
                return ctlSrcDev.tbRangePer;
            }
            set
            {
                ctlSrcDev.tbRangePer = value;
            }
        }

        /// <summary>
        /// 被引导站距离显示属性
        /// </summary>
        public string tarDevRange
        {
            get
            {
                return ctlTarDev.tbRangePer;
            }
            set
            {
                ctlTarDev.tbRangePer = value;
            }
        }

        public string Biaoshi
        {
            get
            {
                return biaoshi;
            }
            set
            {
                biaoshi = value;
                tbBiaoshi.Text = value;
            }
        }

        public string Daihao
        {
            get
            {
                return daihao;
            }
            set
            {
                daihao = value;
                tbDaihao.Text = value;
            }
        }

        private void btnRemove_Click(object sender, EventArgs e)
        {
            // 停止接收线程
            leadingProcess.netRecvManagerDict[sourceDevName].AbortThread();
            // 重启timer1计时器，使得tbTime显示本机时间
            Form1.newUI.timer1.Start();
            this.Parent.Controls.Remove(this);
        }

        private void btnStop_Click(object sender, EventArgs e)
        {
            leadingProcess.groupNetSendThreadDict[tarDevName].AbortThread();
            isRunning = false;
            btnStart.Enabled = true;
            btnStop.Enabled = false;

            ctlSrcDev.tbAZ.ForeColor = Color.Yellow;
            ctlSrcDev.tbEL.ForeColor = Color.Yellow;
            ctlTarDev.tbAZ.ForeColor = Color.Yellow;
            ctlTarDev.tbEL.ForeColor = Color.Yellow;
            lbRunningArror.ForeColor = Color.Black;
        }

        private void btnStart_Click(object sender, EventArgs e)
        {
            isRunning = true;
            GroupNetSendThread gnst = leadingProcess.groupNetSendThreadDict[tarDevName];
            gnst.RunThread();
            btnStart.Enabled = false;
            btnStop.Enabled = true;

            ctlSrcDev.tbAZ.ForeColor = Color.Lime;
            ctlSrcDev.tbEL.ForeColor = Color.Lime;
            ctlTarDev.tbAZ.ForeColor = Color.Lime;
            ctlTarDev.tbEL.ForeColor = Color.Lime;
            lbRunningArror.ForeColor = Color.Lime;
        }

        private void timerNull_Tick(object sender, EventArgs e)
        {
            srcDevAZ = "NULL";
            srcDevEL = "NULL";
            tarDevAZ = "NULL";
            tarDevEL = "NULL";
        }

        /// <summary>
        /// 获取焦点后改变颜色
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        public void LeadingRow_Enter(object sender, EventArgs e)
        {
            BackColor = SystemColors.Highlight;
            selected = true;
            foreach (LeadingRow row in Parent.Controls)
            {
                if (row == this) continue;
                row.selected = false;
                row.BackColor = SystemColors.Control;
            }
        }
    }
}
