﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Collections;
using System.Diagnostics;
using System.Xml;
using CrossLeading.Controls;

namespace CrossLeading.Net
{
    public delegate void NetRecvEventHandle(byte[] byteArr, bool usingQD, uint qdSrcUacNumber, uint qdTarUacNumber, List<LeadingRow> leadingRows);

    public class NetRecvManager
    {
        //private static NetRecvManager _NetMagr = null;
        private NetRecvEventHandle _hNetEvent = null;
        private IPAddress _mcastAddress;
        private int _mcastPort;
        private Socket _mcastSocket = null;
        private MulticastOption _mcastOption;
        private byte[] _Recvbuffer = new byte[2048];
        private Thread _NetThread = null;
        private bool boolExit = false;
        private EventWaitHandle _RecvInfoEvent = null;
        private SortedList srtLstSpch = new SortedList();

        private string strIpAddress;
        private string strGroupAddress;
        private int iPort;
        /// <summary>
        /// 是否使用QD作为引导源
        /// </summary>
        public bool usingQD;
        /// <summary>
        /// 是否向QD发送引导
        /// </summary>
        public bool sendingQD;
        public uint qdSrcUacNumber;
        public uint qdTarUacNumber;
        public List<LeadingRow> leadingRowControls = new List<LeadingRow>();

        public NetRecvManager(NetRecvEventHandle NetEvent, string strGroup, string strIp, int nPort)
        {
            _hNetEvent = NetEvent;
            strGroupAddress = strGroup;
            strIpAddress = strIp;
            iPort = nPort;
        }

        /// <summary>
        /// 获取唯一实例
        /// </summary>
        /// <returns></returns>
        //public static NetRecvManager GetInstance(NetRecvEventHandle NetEvent, string strGroup, string strIp, int nPort)
        //{
        //    if (_NetMagr == null)
        //    {
        //        _NetMagr = new NetRecvManager(NetEvent);
        //        try
        //        {

        //            _NetMagr.RunThread("");

        //        }
        //        catch (Exception ee)
        //        {
        //            _NetMagr = null;
        //        }
        //    }
        //    return _NetMagr;
        //}

        /// <summary>
        /// 
        /// </summary>
        //public static NetRecvManager Instance
        //{
        //    get
        //    {
        //        return _NetMagr;
        //    }
        //}
        /// <summary>
        /// 获取配置信息
        /// </summary>
        /// <param name="strGroupAddress"></param>
        /// <param name="strIpAddress"></param>
        /// <param name="port"></param>
        /// <returns></returns>
        //private bool GetRecvConfig(out string strGroupAddress, out string strIpAddress, out int port)
        //{
        //    strGroupAddress = "";   //收组播
        //    strIpAddress = "";
        //    port = 0;

        //    string xmlName = System.Windows.Forms.Application.StartupPath + "\\Config.xml";
        //    //初始化数据集合
        //    if (!System.IO.File.Exists(xmlName))
        //    {
        //        throw new Exception(string.Format("{0}", "路径不存在"));
        //    }
        //    XmlDocument doc = new XmlDocument();
        //    doc.Load(xmlName);
        //    foreach (XmlNode node in doc.SelectNodes("//RecvGroup"))
        //    {
        //        strGroupAddress = node.SelectSingleNode("GroupAddress").InnerText;
        //        strIpAddress = node.SelectSingleNode("IpAddress").InnerText;
        //        port = int.Parse(node.SelectSingleNode("Port").InnerText);
        //    }
        //    return true;
        //}

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        private bool InitRecv()
        {
//             string strIpAddress;
//             string strGroupAddress;
//             int iPort;
            try
            {
            //    GetRecvConfig(out strGroupAddress, out strIpAddress, out iPort);

                _mcastAddress = IPAddress.Parse(strGroupAddress);
                _mcastPort = iPort;
                _mcastSocket = new Socket(AddressFamily.InterNetwork,
                                       SocketType.Dgram,
                                       ProtocolType.Udp);
                _mcastSocket.SetSocketOption(SocketOptionLevel.Socket,
                         SocketOptionName.ReuseAddress,
                         true);
                //要加此句,否则本地启动的接收端数量大于1时有冲突

                IPAddress localAddress = IPAddress.Parse(strIpAddress);
                EndPoint localEP = (EndPoint)new IPEndPoint(localAddress, _mcastPort);
                _mcastSocket.Bind(localEP);
                _mcastOption = new MulticastOption(_mcastAddress, localAddress);

                _mcastSocket.SetSocketOption(SocketOptionLevel.IP,
                                         SocketOptionName.AddMembership,
                                         _mcastOption);
            }
            catch (Exception ee)
            {
                Console.WriteLine(ee.Message);                
                return false;
            }
            return true;
        }

        private void Recv()
        {
            EndPoint remoteEP = (EndPoint)new IPEndPoint(IPAddress.Any, 0);
            Object thisLock = new Object();

            try
            {
                while (!boolExit)
                {
                    int num = _mcastSocket.ReceiveFrom(_Recvbuffer, ref remoteEP);
                    if (num >= 10)
                    {
                        int len = num;
                        lock (thisLock)
                        {

                        }
                        List<byte> byteArr = new List<byte>();
                        // 这个for循环的意义是啥？
                        for (int i = 0; i < num; i++)
                        {
                            byteArr.Add(_Recvbuffer[i]);
                        }
                        _hNetEvent(_Recvbuffer, usingQD, qdSrcUacNumber, qdTarUacNumber, leadingRowControls);
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.WriteLine("接收出现异常");
            }

        }

        #region 启动线程
        public void RunThread()
        {
            if (!InitRecv())
            {
                System.Windows.Forms.MessageBox.Show("无法创建组地址网络收！");
                // throw new Exception("无法创建组地址网络收！");
            }

            try
            {
                _RecvInfoEvent = EventWaitHandle.OpenExisting("RecvInfo");
            }
            catch (WaitHandleCannotBeOpenedException)
            {
                _RecvInfoEvent = new EventWaitHandle(false, EventResetMode.AutoReset, "RecvInfo");
            }
            _NetThread = new Thread(new ThreadStart(Recv));
            _NetThread.IsBackground = true;
            _NetThread.Priority = ThreadPriority.AboveNormal;
            if (_NetThread != null)
                _NetThread.Start();
        }
        #endregion
        
        #region 终止线程
        public void AbortThread()
        {
            // boolExit = true;
            if (_mcastSocket != null)
            {
                _mcastSocket.Close();
            }
            if (_RecvInfoEvent != null)
            {
                _RecvInfoEvent.Close();
            }
            if (_NetThread != null)
            {
                _NetThread.Abort();
            }
        }
        #endregion

    }
   
}