using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows.Forms;
using CrossLeading.Models;

namespace CrossLeading.Forms
{
    public partial class SatelliteManagementForm : Form
    {
        private List<SatelliteOrbitElement> satellites;
        private string selectedSatelliteName = "";

        public SatelliteManagementForm()
        {
            InitializeComponent();
        }

        private void SatelliteManagementForm_Load(object sender, EventArgs e)
        {
            LoadSatelliteList();
            ClearInputFields();
            UpdateButtonStates();
        }

        /// <summary>
        /// 加载卫星列表
        /// </summary>
        private void LoadSatelliteList()
        {
            try
            {
                satellites = SatelliteOrbitManager.LoadAllSatellites();
                RefreshListView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载卫星列表失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 刷新ListView显示
        /// </summary>
        private void RefreshListView()
        {
            listViewSatellites.Items.Clear();

            foreach (var satellite in satellites)
            {
                ListViewItem item = new ListViewItem(satellite.SatelliteName);
                item.SubItems.Add(satellite.GetEpochTime().ToString("yyyy-MM-dd HH:mm:ss"));
                item.SubItems.Add(satellite.SemiMajorAxis.ToString("F2"));
                item.SubItems.Add(satellite.Inclination.ToString("F2"));
                item.Tag = satellite;
                listViewSatellites.Items.Add(item);
            }
        }

        /// <summary>
        /// 清空输入字段
        /// </summary>
        private void ClearInputFields()
        {
            txtSatelliteName.Text = "";
            dtpEpochTime.Value = DateTime.Now;
            txtSemiMajorAxis.Text = "7000.0";
            txtEccentricity.Text = "0.0";
            txtInclination.Text = "0.0";
            txtRAAN.Text = "0.0";
            txtArgumentOfPerigee.Text = "0.0";
            txtMeanAnomaly.Text = "0.0";
            selectedSatelliteName = "";
        }

        /// <summary>
        /// 更新按钮状态
        /// </summary>
        private void UpdateButtonStates()
        {
            bool hasSelection = listViewSatellites.SelectedItems.Count > 0;
            btnUpdate.Enabled = hasSelection;
            btnDelete.Enabled = hasSelection;
        }

        /// <summary>
        /// 从输入字段创建卫星轨道根数对象
        /// </summary>
        /// <returns>卫星轨道根数对象</returns>
        private SatelliteOrbitElement CreateSatelliteFromInput()
        {
            try
            {
                var satellite = new SatelliteOrbitElement();
                satellite.SatelliteName = txtSatelliteName.Text.Trim();
                satellite.SetEpochTime(dtpEpochTime.Value);
                satellite.SemiMajorAxis = double.Parse(txtSemiMajorAxis.Text);
                satellite.Eccentricity = double.Parse(txtEccentricity.Text);
                satellite.Inclination = double.Parse(txtInclination.Text);
                satellite.RAAN = double.Parse(txtRAAN.Text);
                satellite.ArgumentOfPerigee = double.Parse(txtArgumentOfPerigee.Text);
                satellite.MeanAnomaly = double.Parse(txtMeanAnomaly.Text);
                return satellite;
            }
            catch (Exception ex)
            {
                throw new ArgumentException($"输入数据格式错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 将卫星轨道根数填充到输入字段
        /// </summary>
        /// <param name="satellite">卫星轨道根数对象</param>
        private void FillInputFields(SatelliteOrbitElement satellite)
        {
            if (satellite == null) return;

            txtSatelliteName.Text = satellite.SatelliteName;
            dtpEpochTime.Value = satellite.GetEpochTime();
            txtSemiMajorAxis.Text = satellite.SemiMajorAxis.ToString();
            txtEccentricity.Text = satellite.Eccentricity.ToString("F6");
            txtInclination.Text = satellite.Inclination.ToString("F4");
            txtRAAN.Text = satellite.RAAN.ToString("F4");
            txtArgumentOfPerigee.Text = satellite.ArgumentOfPerigee.ToString("F4");
            txtMeanAnomaly.Text = satellite.MeanAnomaly.ToString("F4");
        }

        private void listViewSatellites_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listViewSatellites.SelectedItems.Count > 0)
            {
                var selectedItem = listViewSatellites.SelectedItems[0];
                var satellite = (SatelliteOrbitElement)selectedItem.Tag;
                selectedSatelliteName = satellite.SatelliteName;
                FillInputFields(satellite);
            }
            else
            {
                selectedSatelliteName = "";
            }
            UpdateButtonStates();
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                var satellite = CreateSatelliteFromInput();
                if (SatelliteOrbitManager.AddSatellite(satellite))
                {
                    LoadSatelliteList();
                    ClearInputFields();
                    MessageBox.Show("卫星添加成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加卫星失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnUpdate_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(selectedSatelliteName))
            {
                MessageBox.Show("请先选择要修改的卫星！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var satellite = CreateSatelliteFromInput();
                if (SatelliteOrbitManager.UpdateSatellite(selectedSatelliteName, satellite))
                {
                    LoadSatelliteList();
                    ClearInputFields();
                    MessageBox.Show("卫星修改成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"修改卫星失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(selectedSatelliteName))
            {
                MessageBox.Show("请先选择要删除的卫星！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            DialogResult result = MessageBox.Show($"确定要删除卫星 '{selectedSatelliteName}' 吗？", 
                "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                if (SatelliteOrbitManager.DeleteSatellite(selectedSatelliteName))
                {
                    LoadSatelliteList();
                    ClearInputFields();
                    MessageBox.Show("卫星删除成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearInputFields();
            listViewSatellites.SelectedItems.Clear();
            UpdateButtonStates();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
