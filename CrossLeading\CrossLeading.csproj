﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{5FFA7B90-962F-4DA1-A294-A1A3C47A3E54}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>CrossLeading</RootNamespace>
    <AssemblyName>CrossLeading</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <TargetFrameworkProfile>
    </TargetFrameworkProfile>
    <FileAlignment>512</FileAlignment>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>Resource\ICO.ICO</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.XML" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Af.Winform.CardLayout.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="AfWinform.GraphicUtil.cs" />
    <Compile Include="Cards\DeviceConfig.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Cards\DeviceConfig.Designer.cs">
      <DependentUpon>DeviceConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="Cards\NewUI.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Cards\NewUI.Designer.cs">
      <DependentUpon>NewUI.cs</DependentUpon>
    </Compile>
    <Compile Include="Cards\VisibilityPredictCard.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Cards\VisibilityPredictCard.Designer.cs">
      <DependentUpon>VisibilityPredictCard.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\DeviceControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\DeviceControl.Designer.cs">
      <DependentUpon>DeviceControl.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\LeadingRow.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\LeadingRow.Designer.cs">
      <DependentUpon>LeadingRow.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\DevEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DevEditForm.Designer.cs">
      <DependentUpon>DevEditForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="Cards\FormMain.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Cards\FormMain.Designer.cs">
      <DependentUpon>FormMain.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UACEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UACEditForm.Designer.cs">
      <DependentUpon>UACEditForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SatelliteManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SatelliteManagementForm.Designer.cs">
      <DependentUpon>SatelliteManagementForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Models\SatelliteOrbitElement.cs" />
    <Compile Include="Models\SatelliteOrbitManager.cs" />
    <Compile Include="FuncItem.cs" />
    <Compile Include="FuncMenu.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Net\FrameHBL.cs" />
    <Compile Include="Net\LeadingProcess.cs" />
    <Compile Include="Net\NetRecvThread.cs" />
    <Compile Include="Net\NetRecvThreadOld.cs" />
    <Compile Include="Net\NetSendThread.cs" />
    <Compile Include="Net\NetSendThreadOld.cs" />
    <Compile Include="OrbitForcast.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Utils\Utilites.cs" />
    <Compile Include="CoordinateTransformTest.cs" />
    <EmbeddedResource Include="Cards\DeviceConfig.resx">
      <DependentUpon>DeviceConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Cards\NewUI.resx">
      <DependentUpon>NewUI.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Cards\VisibilityPredictCard.resx">
      <DependentUpon>VisibilityPredictCard.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\DeviceControl.resx">
      <DependentUpon>DeviceControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\LeadingRow.resx">
      <DependentUpon>LeadingRow.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\DevEditForm.resx">
      <DependentUpon>DevEditForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Cards\FormMain.resx">
      <DependentUpon>FormMain.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UACEditForm.resx">
      <DependentUpon>UACEditForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="app.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Satellite.bmp" />
    <Content Include="Resource\Client.png" />
    <Content Include="Resource\Detail.png" />
    <Content Include="Resource\Help.png" />
    <Content Include="Resource\ICO.ICO" />
    <Content Include="Resource\icons8-config-64.png" />
    <Content Include="Resource\icons8-rocket-64.png" />
    <Content Include="Resource\icons8-satellite-64.png" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>