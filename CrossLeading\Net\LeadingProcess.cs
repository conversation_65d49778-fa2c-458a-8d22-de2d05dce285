using CrossLeading.Controls;
using MyListBox;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;

namespace CrossLeading.Net
{
    /// <summary>
    /// 距离预报数据点
    /// </summary>
    public struct RangeForecastData
    {
        public DateTime Time;
        public double Range; // 距离 (km)
    }

    /// <summary>
    /// 距离预报数据管理器
    /// </summary>
    public static class RangeForecastManager
    {
        private static Dictionary<string, List<RangeForecastData>> stationRangeData =
            new Dictionary<string, List<RangeForecastData>>();
        private static readonly object lockObject = new object();

        /// <summary>
        /// 更新测站的距离预报数据
        /// </summary>
        /// <param name="stationName">测站名称</param>
        /// <param name="rangeData">距离预报数据列表</param>
        public static void UpdateStationRangeData(string stationName, List<RangeForecastData> rangeData)
        {
            lock (lockObject)
            {
                if (stationRangeData.ContainsKey(stationName))
                {
                    stationRangeData[stationName] = rangeData;
                }
                else
                {
                    stationRangeData.Add(stationName, rangeData);
                }
            }
        }

        /// <summary>
        /// 获取指定时间的距离预报值（带插值）
        /// </summary>
        /// <param name="stationName">测站名称</param>
        /// <param name="targetTime">目标时间</param>
        /// <returns>插值后的距离值 (km)，如果没有数据返回-1</returns>
        public static double GetInterpolatedRange(string stationName, DateTime targetTime)
        {
            lock (lockObject)
            {
                if (!stationRangeData.ContainsKey(stationName) || stationRangeData[stationName].Count == 0)
                {
                    return -1; // 没有数据
                }

                var data = stationRangeData[stationName];

                // 查找目标时间前后的数据点
                RangeForecastData? beforeData = null;
                RangeForecastData? afterData = null;

                for (int i = 0; i < data.Count; i++)
                {
                    if (data[i].Time <= targetTime)
                    {
                        beforeData = data[i];
                    }
                    if (data[i].Time >= targetTime && afterData == null)
                    {
                        afterData = data[i];
                        break;
                    }
                }

                // 如果只有一个数据点或目标时间在数据范围外
                if (beforeData == null && afterData == null)
                {
                    return -1;
                }
                if (beforeData != null && afterData == null)
                {
                    return beforeData.Value.Range;
                }
                if (beforeData == null && afterData != null)
                {
                    return afterData.Value.Range;
                }

                // 进行线性插值
                var before = beforeData.Value;
                var after = afterData.Value;

                if (before.Time == after.Time)
                {
                    return before.Range;
                }

                double timeDiff = (targetTime - before.Time).TotalSeconds;
                double totalTimeDiff = (after.Time - before.Time).TotalSeconds;
                double ratio = timeDiff / totalTimeDiff;

                return before.Range + (after.Range - before.Range) * ratio;
            }
        }

        /// <summary>
        /// 清除指定测站的数据
        /// </summary>
        /// <param name="stationName">测站名称</param>
        public static void ClearStationData(string stationName)
        {
            lock (lockObject)
            {
                if (stationRangeData.ContainsKey(stationName))
                {
                    stationRangeData.Remove(stationName);
                }
            }
        }

        /// <summary>
        /// 将W6E时间（积秒）转换为DateTime对象
        /// </summary>
        /// <param name="w6eTime">W6E时间（积秒，以0.1毫秒为单位）</param>
        /// <returns>对应的DateTime对象</returns>
        public static DateTime ConvertW6ETimeToDateTime(uint w6eTime)
        {
            try
            {
                // 使用JS2HMS函数将积秒转换为时分秒
                int nHour, nMinute, nSecond;
                double dMS;

                if (LeadingProcess.JS2HMS(w6eTime, out nHour, out nMinute, out nSecond, out dMS) == 0)
                {
                    // 获取今天的日期
                    DateTime today = DateTime.Today;

                    // 构造完整的DateTime对象
                    DateTime result = new DateTime(today.Year, today.Month, today.Day,
                                                 nHour, nMinute, nSecond);

                    // 添加毫秒部分
                    result = result.AddMilliseconds(dMS);

                    return result;
                }
                else
                {
                    // 如果转换失败，返回当前时间
                    return DateTime.Now;
                }
            }
            catch (Exception)
            {
                // 异常情况下返回当前时间
                return DateTime.Now;
            }
        }
    }

    /// <summary>
    /// EMBL数据帧头格式
    /// </summary>
    public struct EMBLHead
    {
        /// <summary>
        /// 积秒
        /// </summary>
        public uint Time;
        /// <summary>
        /// 任务标识
        /// </summary>
        public uint MID;
        /// <summary>
        /// 信息类别
        /// </summary>
        public uint BID;
        /// <summary>
        /// 保留字段1
        /// </summary>
        public uint Res1;
        /// <summary>
        /// 保留字段2
        /// </summary>
        public uint Res2;
        /// <summary>
        /// 数据字段长度
        /// </summary>
        public uint LEN;
    }
    /// <summary>
    /// EMBL数据帧头格式
    /// </summary>
    public struct EMBLHeadRadar
    {
        /// <summary>
        /// 信息类别
        /// </summary>
        public uint BID;
        /// <summary>
        /// 积秒
        /// </summary>
        public ushort wDay;
        /// <summary>
        /// 任务代号
        /// </summary>
        public uint time;
        /// <summary>
        /// 保留字段1
        /// </summary>
        public byte[] Res;
        /// <summary>
        /// 数据字段长度
        /// </summary>
        public ushort LEN;
    }
    /// <summary>
    /// 测角数据
    /// </summary>
    public struct W6EData
    {
        /// <summary>
        /// 状态：自跟踪/非自跟踪
        /// </summary>
        public string ZT;
        /// <summary>
        /// 时标
        /// </summary>
        public uint Time;
        /// <summary>
        /// 方位（度）
        /// </summary>
        public double AZ;
        /// <summary>
        /// 俯仰（度）
        /// </summary>
        public double EL;
    }
    public struct W3CData
    {
        /// <summary>
        /// 状态：自跟踪/非自跟踪
        /// </summary>
        public string ZT;
        /// <summary>
        /// 时标
        /// </summary>
        public uint Time;
        /// <summary>
        /// 方位（度）
        /// </summary>
        public double AZ;
        /// <summary>
        /// 俯仰（度）
        /// </summary>
        public double EL;
    }

    /// <summary>
    /// 负责引导数据转换相关逻辑
    /// </summary>
    public class LeadingProcess
    {
        /// <summary>
        /// 存储<设备代号, 收线程处理器>键值对
        /// </summary>
        public Dictionary<string, NetRecvManager> netRecvManagerDict = new Dictionary<string, NetRecvManager>();
        /// <summary>
        /// 存储<设备代号, 发线程处理器>键值对
        /// </summary>
        public Dictionary<string, GroupNetSendThread> groupNetSendThreadDict = new Dictionary<string, GroupNetSendThread>();

        public LeadingRow leadingRow;
        public bool sendingQD;
        public LeadingProcess(LeadingRow row)
        {
            leadingRow = row;
        }
        /// <summary>
        /// 初始化引导源设备的网络收线程管理器(NetRecvManager)、和被引导设备的网络发线程管理器(GroupNetSendThread)
        /// </summary>
        public void CreateNetManager(string srcDevName, string tarDevName)
        {
            if (srcDevName.StartsWith("QD"))
            {
                srcDevName = srcDevName.Substring(0, 7);
            }
            if (tarDevName.StartsWith("QD"))
            {
                tarDevName = tarDevName.Substring(0, 7);
            }
            DevConf srcDevConf = Form1.devConfDict[srcDevName];

            // 创建NetRecvManager
            NetRecvManager nrm = new NetRecvManager(
                RecvNetInfo,
                srcDevConf.gpRecvAddr,
                srcDevConf.dataIPAddr,
                (int)srcDevConf.gpRecvPort
            );
            netRecvManagerDict.Add(srcDevName, nrm);

            DevConf tarDevConf = Form1.devConfDict[tarDevName];
            // 创建GroupNetSendThread
            GroupNetSendThread gnst = new GroupNetSendThread(
                tarDevConf.gpSendAddr,
                tarDevConf.dataIPAddr,
                (int)tarDevConf.gpSendPort
            );
            groupNetSendThreadDict.Add(tarDevName, gnst);
        }

        /// <summary>
        /// 创建网络收线程管理器
        /// </summary>
        /// <param name="leadSrc">引导源装备名称</param>
        /// <param name="leadTar">被引导装备名称</param>
        /// <param name="row">引导操作控件实例</param>
        public void CreateRecvNetThread(string leadSrc, string leadTar)
        {
            Console.WriteLine(leadSrc + " ---> " + leadTar);
            // 如果引导源是QD，需要根据波束号区分是哪个波束的数据
            bool usingQD = false;
            bool sendingQD = false;
            int qdBeamNo = 0; // QD波束号（1-16）
            uint qdSrcUacAddr = 0;
            uint qdTarUacAddr = 0;
            if (leadSrc.StartsWith("QD"))
            {
                usingQD = true;
                qdBeamNo = int.Parse(leadSrc.Substring(8)); // QD-4203-1的第8位之后
                leadSrc = "QD-4203";
                // 将字符串表示的uac地址转换为uint十进制数字
                string s_UacAddr = Form1.deviceConfigCard.uacConfList.Items[qdBeamNo - 1].SubItems[2].Text.ToString();
                if (s_UacAddr.StartsWith("0x"))
                    s_UacAddr = s_UacAddr.Substring(2);
                qdSrcUacAddr = uint.Parse(
                    s_UacAddr,
                    NumberStyles.HexNumber,
                    CultureInfo.InvariantCulture
                );
            }
            if (leadTar.StartsWith("QD"))
            {
                sendingQD = true;
                qdBeamNo = int.Parse(leadTar.Substring(8)); // QD-4203-1的第8位之后
                leadTar = "QD-4203";
                // 将字符串表示的uac地址转换为uint十进制数字
                string s_UacAddr = Form1.deviceConfigCard.uacConfList.Items[qdBeamNo - 1].SubItems[2].Text.ToString();
                if (s_UacAddr.StartsWith("0x"))
                    s_UacAddr = s_UacAddr.Substring(2);
                qdTarUacAddr = uint.Parse(
                    s_UacAddr,
                    NumberStyles.HexNumber,
                    CultureInfo.InvariantCulture
                );
            }

            NetRecvManager netRecvManager = netRecvManagerDict[leadSrc];
            netRecvManager.usingQD = usingQD; 
            netRecvManager.sendingQD = sendingQD; 
            netRecvManager.qdSrcUacNumber = qdSrcUacAddr;
            netRecvManager.qdTarUacNumber = qdTarUacAddr;
            netRecvManager.leadingRowControls.Add(leadingRow);

            netRecvManager.RunThread(); // 实际上运行的就是RecvNetInfo函数
        }

        #region 数据格式转换
        /// <summary>
        /// EMBL包头格式转换
        /// </summary>
        /// <param name="byteArr"></param>
        /// <returns></returns>
        public static EMBLHead FormateHead(byte[] byteArr)
        {
            EMBLHead EMBLHeader = new EMBLHead();
            IntPtr buffer = Marshal.AllocCoTaskMem(24);
            Marshal.Copy(byteArr, 0, buffer, 24);
            EMBLHeader.Time = (uint)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 0), typeof(uint));
            EMBLHeader.MID = (uint)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 4), typeof(uint));
            EMBLHeader.BID = (uint)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 8), typeof(uint));
            EMBLHeader.Res1 = (uint)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 12), typeof(uint));
            EMBLHeader.Res2 = (uint)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 16), typeof(uint));
            EMBLHeader.LEN = (uint)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 20), typeof(uint));

            Marshal.FreeHGlobal(buffer);

            return EMBLHeader;
        }

        public static W6EData FormtW6E(byte[] buff)
        {
            W6EData W6Edata = new W6EData();
            IntPtr buffer = Marshal.AllocCoTaskMem(23);
            Marshal.Copy(buff, 24, buffer, 23);
            byte br, zt;
            zt = (byte)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 0), typeof(byte));
            br = (byte)(zt & 0x02);
            if (br == 0x02)
                W6Edata.ZT = "自跟踪";
            else
                W6Edata.ZT = "非自跟踪";
            W6Edata.Time = (uint)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 1), typeof(uint));
            uint tmp = (uint)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 5), typeof(uint));
            W6Edata.AZ = tmp / Math.Pow(2, 32) * 360;
            tmp = (uint)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 9), typeof(uint));
            W6Edata.EL = tmp / Math.Pow(2, 32) * 360;

            return W6Edata;
        }
        public static W3CData FormtW3C(byte[] buff)    //213数据
        {
            W3CData W3Cdata = new W3CData();
            IntPtr buffer = Marshal.AllocCoTaskMem(167);
            Marshal.Copy(buff, 0, buffer, 167);
            W3Cdata.Time = (uint)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 6), typeof(uint));

            byte workmode = (byte)Marshal.PtrToStructure(new IntPtr(buffer.ToInt32() + 28), typeof(byte));
            if (workmode == 0x02) //増程模式
            {
                double tmp = (double)Marshal.PtrToStructure(new IntPtr(buffer.ToInt64() + 133), typeof(double));
                W3Cdata.AZ = tmp;
                tmp = (double)Marshal.PtrToStructure(new IntPtr(buffer.ToInt64() + 141), typeof(double));
                W3Cdata.EL = tmp;

            }
            else //正常模式
            {
                float tmp = (float)Marshal.PtrToStructure(new IntPtr(buffer.ToInt64() + 40), typeof(float));
                W3Cdata.AZ = tmp;
                tmp = (float)Marshal.PtrToStructure(new IntPtr(buffer.ToInt64() + 44), typeof(float));
                W3Cdata.EL = tmp;
            }

            return W3Cdata;
        }
        #endregion
        #region 时间转换函数
        /// <summary>
        /// 积秒-->时分秒毫秒
        /// </summary>
        /// <param name="dJS">积秒（0.1ms)</param>
        /// <param name="nHour">时</param>
        /// <param name="nMinute">分</param>
        /// <param name="nSecond">秒</param>
        /// <param name="dMS">毫秒(小数)</param>
        /// <returns>返回0:计算正常</returns>
        public static int JS2HMS(uint dJS, out int nHour, out int nMinute, out int nSecond, out double dMS)
        {
            if (dJS < 0)
            {
                nHour = 0;
                nMinute = 0;
                nSecond = 0;
                dMS = 0.0;
                return -1;
            }

            double dTemp = dJS / 10;
            int nTemp = (int)dTemp;
            nHour = nTemp / 3600000;
            nMinute = (nTemp % 3600000) / 60000;
            nSecond = (nTemp % 60000) / 1000;
            dMS = dJS % 10000 / 10;
            return 0;
        }
        /// <summary>
        /// 时分秒毫秒-->积秒
        /// 功能：根据时、分、秒、毫秒计算当日积秒，并输出
        /// 输出：积秒，以0.1毫秒为单位
        /// </summary>
        /// <param name="nHour"></param>
        /// <param name="nMinute"></param>
        /// <param name="nSecond"></param>
        /// <param name="dMS"></param>
        /// <returns></returns>
        public static uint GetJS(int nHour, int nMinute, int nSecond, double dMS)
        {
            uint uResult = 0;
            uResult = (uint)((((nHour * 60 + nMinute) * 60 + nSecond) * 1000 + dMS) * 10);
            return uResult;
        }
        /// <summary>
        /// 积日-->年月日
        /// </summary>
        /// <param name="nJD">积日,相对2000.0.0</param>
        /// <param name="nYear">年</param>
        /// <param name="nMonth">月</param>
        /// <param name="nDay">日</param>
        public static void JD2YMD(int nJD, out int nYear, out int nMonth, out int nDay)
        {
            int l, m, n, i, j;
            l = nJD + 18294 + 18262;
            i = (int)Math.Floor((l * 4 - 365.0) / 1461.0);
            j = (int)(l - Math.Floor(i * 365.251));
            if (j < 92)
            {
                i = i - 1; j = j + 365;
            }
            m = (int)Math.Floor((j * 5 + 1.5) / 153.0);
            n = (int)(j - Math.Floor((m * 153.0 - 1.5) / 5.0));
            if (m > 12)
            {
                m = m - 12; i = i + 1;
            }
            nYear = 1900 + i;
            nMonth = m;
            nDay = n;
        }
        /// <summary>
        /// 年月日-->积日
        /// 功能：根据年、月、日计算积日，并输出
        ///	输入：b2000 = TRUE: 相对于2000年标志，否则为对应于1950
        /// </summary>
        /// <param name="nYear"></param>
        /// <param name="nMonth"></param>
        /// <param name="nDay"></param>
        /// <param name="b2000"></param>
        /// <returns></returns>
        public static int GetJD(int nYear, int nMonth, int nDay, bool b2000)
        {
            int nResult = 0;
            if (nYear < 1950)
                return 0;
            if (nMonth < 3)
            {
                nMonth += 12;
                nYear--;
            }
            nYear = (int)((nYear - 1900) * 365.25);
            nMonth = (int)((nMonth * 153.0 - 1.5) / 5);
            nResult = nYear + nMonth + nDay;

            if (b2000)
                return (nResult - 18294 - 18262);
            else
                return (nResult - 18294);
        }

        #endregion

        /// <summary>
        /// 数据接收处理函数
        /// </summary>
        /// <param name="byteArr">接收的数据</param>
        /// <param name="usingQD">是否是多波束设备</param>
        /// <param name="qdSrcUacNumber">多波束源UAC地址</param>
        /// <param name="qdTarUacNumber">多波束目标UAC地址</param>
        /// <param name="leadingRows"></param>
        public void RecvNetInfo(byte[] byteArr, bool usingQD, uint qdSrcUacNumber, uint qdTarUacNumber, List<LeadingRow> leadingRows)
        {
            leadingRow.timerNull.Stop();
            Form1.newUI.timer1.Start();
            
            //////////////////////////////////////////////////////////////////////////
            // 转换EMBL包头
            EMBLHead emblHead = FormateHead(byteArr);

            int nHour, nMinute, nSecond;
            double dMS;
            //////////////////////////////////////////////////////////////////////////
            // 转换W6E数据，并显示
            // 0x00110111 0x000000f0 //16进制00100605 == 10进制1050117；0x00007E00 == 32256（10进制）
            if (emblHead.BID != 0x00100605 && emblHead.BID != 0x00007E00)
            {
                return;
            }

            uint uacTmp = emblHead.Res1;
            if (usingQD && uacTmp != qdSrcUacNumber) // 丢弃非选定波束来源的数据
            {
                return;
            }

            // TODO：处理TS-4214数据，未完成
            if (leadingRow.sourceDevName == "TS-4214" && emblHead.MID != Convert.ToInt32(leadingRow.biaoshi, 16))
            {
                return;
            }
            Form1.newUI.timer1.Stop();

            W6EData W6Edata = FormtW6E(byteArr);
            JS2HMS(W6Edata.Time, out nHour, out nMinute, out nSecond, out dMS);
            string tm = string.Format("{0:00}:{1:00}:{2:00}.{3:0000}", nHour, nMinute, nSecond, dMS * 10);
            // Form1.newUI.tbTime.Text = tm;
            Form1.newUI.SetText(Form1.newUI.tbTime, tm);

            foreach (LeadingRow row in leadingRows)
            {
                // 以线程安全方式改变textBox内容
                //Form1.newUI.SetText(row.ctlSrcDev.tbAZ, W6Edata.AZ.ToString("0.000"));
                //Form1.newUI.SetText(row.ctlSrcDev.tbEL, W6Edata.EL.ToString("0.000"));
                row.ctlSrcDev.tbAZ.Invoke((MethodInvoker)delegate
                {
                    row.ctlSrcDev.tbAZ.Text = W6Edata.AZ.ToString("0.000");
                });
                row.ctlSrcDev.tbEL.Invoke((MethodInvoker)delegate
                {
                    row.ctlSrcDev.tbEL.Text = W6Edata.EL.ToString("0.000");
                });
                
                // 获取当前时间对应的实时距离预报值
                // 根据W6Edata.Time进行更精确的时间计算
                DateTime currentTime = RangeForecastManager.ConvertW6ETimeToDateTime(W6Edata.Time);

                // 更新引导源的距离显示
                row.ctlSrcDev.UpdateRangeDisplay(currentTime);
                string srcDevName = row.ctlSrcDev.devName;
                double realTimeRange = RangeForecastManager.GetInterpolatedRange(srcDevName, currentTime);

                // 如果没有实时预报数据，使用原来的固定距离值
                double Range;
                if (realTimeRange > 0)
                {
                    Range = realTimeRange * 1000; // 转换为米
                }
                else
                {
                    Range = Convert.ToDouble(row.forcaseRange) * 1000; // 使用原来的固定值
                }

                //////////////////////////////////////////////////////////////////////////
                // 坐标变换(1) 产生XYZ
                double[] RAE = new double[6];
                RAE[0] = Range;
                RAE[1] = W6Edata.AZ;
                RAE[2] = W6Edata.EL;

                // srcDevName已在前面定义过了，直接使用
                double lati = Form1.devConfDict[srcDevName].lati;
                double longi = Form1.devConfDict[srcDevName].longi;
                double height = Form1.devConfDict[srcDevName].height;

                double[] SourceBLH = { lati, longi, height };
                double[] staXYZ = OrbitForcast.RAE2staRxyz(RAE);
                double[] Gxyz = OrbitForcast.staRxyz2Gxyz(staXYZ, SourceBLH);

                //////////////////////////////////////////////////////////////////////////
                // 坐标变换(2) 显示反向变换后的角度
                double[] staXYZ2;
                double[] RAE2;

                string tarDevName = row.ctlTarDev.devName;
                lati = Form1.devConfDict[tarDevName].lati;
                longi = Form1.devConfDict[tarDevName].longi;
                height = Form1.devConfDict[tarDevName].height;
                double[] TargetBLH = { lati, longi, height };

                staXYZ2 = OrbitForcast.Gxyz2staRxyz(Gxyz, TargetBLH);
                RAE2 = OrbitForcast.staRxyz2RAE(staXYZ2);

                // 以线程安全方式改变textBox内容
                //Form1.newUI.SetText(row.ctlTarDev.tbAZ, RAE2[1].ToString("0.000"));
                //Form1.newUI.SetText(row.ctlTarDev.tbEL, RAE2[2].ToString("0.000"));
                row.ctlTarDev.tbAZ.Invoke((MethodInvoker)delegate
                {
                    row.ctlTarDev.tbAZ.Text = RAE2[1].ToString("0.000");
                });
                row.ctlTarDev.tbEL.Invoke((MethodInvoker)delegate
                {
                    row.ctlTarDev.tbEL.Text = RAE2[2].ToString("0.000");
                });

                // 更新被引导站的距离显示
                row.ctlTarDev.UpdateRangeDisplay(currentTime);
                //////////////////////////////////////////////////////////////////////////
                // 转发引导
                if (row.isRunning == true)
                {
                    //uint biaoshi = uint.Parse(leadingRow.biaoshi);
                    uint biaoshi = uint.Parse(
                    row.biaoshi,
                    NumberStyles.HexNumber,
                    CultureInfo.InvariantCulture
                );
                    SendP12Data(W6Edata.Time, biaoshi, Gxyz, row, qdTarUacNumber);
                }
            }
            leadingRow.timerNull.Start();
        }

        private void SendP12Data(uint BJtm, uint mid, double[] Gxyz, LeadingRow leadingRow, uint qdTarUacNumber)
        {
            byte[] Buff = new byte[24 + 28];
            IntPtr pnt = Marshal.AllocHGlobal(24 + 28);
            EMBLHead embl = new EMBLHead();
            embl.Time = BJtm;
            embl.MID = mid;
            embl.BID = 0x002A0160;
            embl.Res1 = 0x00;
            // 向qd发送引导数据，需要设置保留字段1为对应的UAC号
            if (qdTarUacNumber != 0)
            {
                embl.Res1 = qdTarUacNumber;
            }
            embl.Res2 = 0x00;
            embl.LEN = 28;
            int tmp;
            int offset = 24;
            try
            {
                Marshal.StructureToPtr(embl.Time, pnt, false);
                Marshal.Copy(pnt, Buff, 0, sizeof(uint));

                Marshal.StructureToPtr(embl.MID, pnt, false);
                Marshal.Copy(pnt, Buff, 4, sizeof(uint));

                Marshal.StructureToPtr(embl.BID, pnt, false);
                Marshal.Copy(pnt, Buff, 8, sizeof(uint));

                Marshal.StructureToPtr(embl.Res1, pnt, false);
                Marshal.Copy(pnt, Buff, 12, sizeof(uint));

                Marshal.StructureToPtr(embl.Res2, pnt, false);
                Marshal.Copy(pnt, Buff, 16, sizeof(uint));

                Marshal.StructureToPtr(embl.LEN, pnt, false);
                Marshal.Copy(pnt, Buff, 20, sizeof(uint));

                //
                Marshal.StructureToPtr(BJtm, pnt, false);
                Marshal.Copy(pnt, Buff, offset + 0, sizeof(uint));

                tmp = Convert.ToInt32(Gxyz[0] * 10);
                Marshal.StructureToPtr(tmp, pnt, false);
                Marshal.Copy(pnt, Buff, offset + 4, sizeof(int));

                tmp = Convert.ToInt32(Gxyz[1] * 10);
                Marshal.StructureToPtr(tmp, pnt, false);
                Marshal.Copy(pnt, Buff, offset + 8, sizeof(int));

                tmp = Convert.ToInt32(Gxyz[2] * 10);
                Marshal.StructureToPtr(tmp, pnt, false);
                Marshal.Copy(pnt, Buff, offset + 12, sizeof(int));

                tmp = Convert.ToInt32(Gxyz[3] * 100);
                Marshal.StructureToPtr(tmp, pnt, false);
                Marshal.Copy(pnt, Buff, offset + 16, 4);

                tmp = Convert.ToInt32(Gxyz[4] * 100);
                Marshal.StructureToPtr(tmp, pnt, false);
                Marshal.Copy(pnt, Buff, offset + 20, 4);

                tmp = Convert.ToInt32(Gxyz[5] * 100);
                Marshal.StructureToPtr(tmp, pnt, false);
                Marshal.Copy(pnt, Buff, offset + 24, 4);
            }
            finally
            {
                Marshal.FreeHGlobal(pnt);
            }

            GroupNetSendThread gnst = groupNetSendThreadDict[leadingRow.tarDevName];
            gnst.SendlinkData(Buff);
        }
    }
}
