﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Runtime.InteropServices;
using System.Xml;

namespace CrossLeading.Net
{
    
    public class GroupNetSendThread
    {
        /// <summary>
        /// 唯一实例
        /// </summary>
        private static GroupNetSendThread _groupNetSend = null;
        private IPAddress _mcastAddress;
        private int _mcastPort;
        private IPEndPoint _groupEP;
        private bool boolExit = false;
        private Socket _mcastSocket = null;
        private MulticastOption _mcastOption;
        private AutoResetEvent[] m_eventSend = null;
    
        private Thread _NetThread = null;

        private string strIpAddress;
        private string strGroupAddress;
        private int iPort;

        public AutoResetEvent[] _eventSend
        {
            get
            {
                return m_eventSend;
            }
        }
        /// <summary>
        /// 获取唯一实例
        /// </summary>
        /// <returns></returns>
        //public static GroupNetSendThread GetInstance()
        //{
        //    if (_groupNetSend == null)
        //    {
        //        _groupNetSend = new GroupNetSendThread();
        //        if (!_groupNetSend.RunThread())
        //        {
        //            return null;
        //        }
        //    }
        //    return _groupNetSend;
        //}

        public GroupNetSendThread(string strGroup, string strIp, int nPort)
        {
            strGroupAddress = strGroup;
            strIpAddress = strIp;
            iPort = nPort;
        }
        /// <summary>
        /// 获取配置信息
        /// </summary>
        /// <param name="strGroupAddress"></param>
        /// <param name="strIpAddress"></param>
        /// <param name="port"></param>
        /// <returns></returns>
        private bool GetSendConfig(out string strGroupAddress, out string strIpAddress, out int port)
        {
            strGroupAddress = "";   //组播
            strIpAddress = "";
            port = 0;

            string xmlName = System.Windows.Forms.Application.StartupPath + "\\Config.xml";
            //初始化数据集合
            if (!System.IO.File.Exists(xmlName))
            {
                throw new Exception(string.Format("{0}", "路径不存在"));
            }
            XmlDocument doc = new XmlDocument();
            doc.Load(xmlName);
            foreach (XmlNode node in doc.SelectNodes("//SendGroup"))
            {
                strGroupAddress = node.SelectSingleNode("GroupAddress").InnerText;
                strIpAddress = node.SelectSingleNode("IpAddress").InnerText;
                port = int.Parse(node.SelectSingleNode("Port").InnerText);
            }
            return true;
        }

        private bool InitSend()
        {
            //string strAddress, strIpAddress;
            //int iSendPort;
            try
            {
                //GetSendConfig(out strAddress, out strIpAddress, out iSendPort);
                _mcastAddress = IPAddress.Parse(strGroupAddress);
                _mcastPort = iPort;
                _mcastSocket = new Socket(AddressFamily.InterNetwork,
                                       SocketType.Dgram, 
                                       ProtocolType.Udp);
                IPAddress localAddress = IPAddress.Parse(strIpAddress);
                EndPoint localEP = (EndPoint)new IPEndPoint(localAddress, 0);
                _mcastSocket.Bind(localEP);
                _mcastOption = new MulticastOption(_mcastAddress, localAddress);
                _mcastSocket.SetSocketOption(SocketOptionLevel.IP,
                                         SocketOptionName.AddMembership,
                                         _mcastOption);

                _groupEP = new IPEndPoint(_mcastAddress, _mcastPort);
            }
            catch (Exception ee)
            {
                Console.WriteLine(ee.Message);
                return false;
            }
            return true;
        }

        public void SendlinkData(byte[] buffer)
        {
            try
            {
                _mcastSocket.SendTo(buffer, _groupEP);
            }
            catch (SocketException e)
            {
                int size = _mcastSocket.SendBufferSize;
                return;
            }
        }

        private void Send(object obj)
        {          
            int index = 0;
            while (!boolExit)
            {               
                index = WaitHandle.WaitAny(
                       m_eventSend, 4000, false);
 
            }
        }
  
        #region 启动线程
        public bool RunThread()
        {
            if (!InitSend())
            {
                System.Windows.Forms.MessageBox.Show("无法创建组地址网络发！");
                return false;
            }
            m_eventSend = new AutoResetEvent[2];
            for (int i = 0; i < m_eventSend.Length; i++)
                m_eventSend[i] = new AutoResetEvent(false);
            _NetThread = new Thread(new ParameterizedThreadStart(Send));
            _NetThread.IsBackground = true;
            if (_NetThread != null)
                _NetThread.Start();
            return true;
        }
        #endregion
        #region 终止线程
        public void AbortThread()
        {
            //_Timer.Dispose();
            boolExit = true;
            if (_mcastSocket != null)
            {
                _mcastSocket.Close();
            }
            if (m_eventSend != null)
            {
                foreach (AutoResetEvent eve in m_eventSend)
                {
                    if (eve != null)
                        eve.Close();
                }
            }
            if (_NetThread != null)
            {
                _NetThread.Abort();

            }

        }
        #endregion
    }
}
