﻿using CrossLeading.Controls;
using MyListBox;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using CrossLeading;
using CrossLeading.Net;
using System.Globalization;
using CrossLeading.Utils;

namespace CrossLeading.Cards
{
    public partial class NewUI : UserControl
    {
        /// <summary>
        /// 时统时间
        /// </summary>
        uint timeBJ;

        public NewUI()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 根据devConfDict设备配置字典初始化源设备和被引导设备的下拉列表项
        /// </summary>
        public void InitCbItems()
        {
            try
            {
                Form1.LoadDevConf();
            }
            catch (Exception e)
            {
                MessageBox.Show("请检查配置文件格式是否正确！");
                // throw new Exception("请检查配置文件格式是否正确！");
            }
            
            cbSrcDev.Items.Clear();
            cbTarDev.Items.Clear();
            foreach (KeyValuePair<string, DevConf> kv in Form1.devConfDict)
            {
                string devName = kv.Key;
                DevConf devConf = kv.Value;
                if (devName.StartsWith("QD") && devConf.isQD)
                {
                    InitQDBeamItems(devName);
                    continue;
                }
                cbSrcDev.Items.Add(kv.Value.devName);
                cbTarDev.Items.Add(kv.Value.devName);
            }
            cbSrcDev.Text = cbSrcDev.Items[0].ToString();
            cbTarDev.Text = cbTarDev.Items[0].ToString();
        }
        /// <summary>
        /// 初始化下拉列表中的QD波束配置
        /// </summary>
        public void InitQDBeamItems(string devName)
        {
            ListView uacLv = Form1.deviceConfigCard.uacConfList;
            foreach(ListViewItem item in uacLv.Items)
            {
                if (item.SubItems[0].Text == devName)
                {
                    cbSrcDev.Items.Add(item.SubItems[1].Text); // 索引0:设备名称，1:UAC名称，2:UAC地址
                    cbTarDev.Items.Add(item.SubItems[1].Text); // 索引0:设备名称，1:UAC名称，2:UAC地址
                }
            }
        }

        delegate void SetTextCallback(TextBox textbx, string text);
        /// <summary>
        /// 以安全方式设置文本控件
        /// </summary>
        /// <param name="textbx"></param>
        /// <param name="str"></param>
        public void SetText(TextBox textbx, string str)
        {
            if (textbx.InvokeRequired)
            {
                SetTextCallback d = new SetTextCallback(SetText);
                try
                {
                    this.Invoke(d, new object[] { textbx, str });
                }
                catch
                {
                    return;
                }
            }
            else
            {
                textbx.Text = str;
            }
        }



        /// <summary>
        /// 创建一个引导控制行组件(LeadingRow),同时创建收组播线程管理器
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnAddRow_Click(object sender, EventArgs e)
        {
            string leadSrc = cbSrcDev.Text.Trim();
            string leadTar = cbTarDev.Text.Trim();
            LeadingRow row = new LeadingRow(
                leadSrc, 
                leadTar, 
                tbMidPer.Text, 
                tb_DaihaoPer.Text, 
                double.Parse(tbRagPer.Text)
            );
            this.leadingPanel.Controls.Add(row);

            LeadingProcess leadingProcess = new LeadingProcess(row);
            row.leadingProcess = leadingProcess;

            leadingProcess.CreateNetManager(leadSrc, leadTar);

            leadingProcess.CreateRecvNetThread(leadSrc, leadTar);
        }

        private void NewUI_Load(object sender, EventArgs e)
        {
            tbTime.Text = DateTime.Now.ToString("HH:mm:ss.ffff");
            timer1.Enabled = true;
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            tbTime.Text = DateTime.Now.ToString("HH:mm:ss.ffff");
        }

        private void bt_setting_Click(object sender, EventArgs e)
        {
            tbDaiHaoShow.Text = tb_DaihaoPer.Text; // 设置用于显示的任务代号
            if (leadingPanel.Controls.Count > 0)
            {
                LeadingRow selectedRow = null;
                foreach (LeadingRow row in leadingPanel.Controls)
                {
                    if (row.selected)
                    {
                        selectedRow = row;
                        break;
                    }
                }
                if (selectedRow == null)
                {
                    for (int i = 0; i < leadingPanel.Controls.Count; i++)
                    {
                        selectedRow = leadingPanel.Controls[0] as LeadingRow;
                    }
                }
                selectedRow.Daihao = tb_DaihaoPer.Text;
                selectedRow.Biaoshi = tbMidPer.Text;
                selectedRow.forcaseRange = double.Parse(tbRagPer.Text);
            }
        }

        private void leadingPanel_Leave(object sender, EventArgs e)
        {
            if (Utilities.IsChildOf(this.ActiveControl, groupBox2))
                return;
            foreach (LeadingRow row in leadingPanel.Controls)
            {
                row.selected = false;
                row.BackColor = SystemColors.Control;
            }
        }
    }
}
