﻿using Af.Winform;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MyListBox
{
    class FuncMenu : ListBox
    {
        public FuncMenu()
        {
            this.DrawMode = DrawMode.OwnerDrawVariable;
        }

        protected override void OnMeasureItem(MeasureItemEventArgs e)
        {
            base.OnMeasureItem(e);
            e.ItemHeight = 100;
        }

        protected override void OnDrawItem(DrawItemEventArgs e)
        {
            base.OnDrawItem(e);
            base.OnDrawItem(e);

            int index = e.Index;
            if (index < 0 || index >= Items.Count) return;

            FuncItem item = (FuncItem)this.Items[index];
            string text = item.text;
            Rectangle rect = e.Bounds;
            Graphics g = e.Graphics;

            // background
            Color bgColor = Color.White;
            Color textColor = Color.FromArgb(44, 44, 44);

            if ((e.State & DrawItemState.Selected) > 0)
            {
                bgColor = Color.FromArgb(100, 240, 240, 240);
                textColor = Color.SkyBlue;
            }

            using (Brush brush = new SolidBrush(bgColor))
            {
                g.Fill<PERSON>ectangle(brush, rect);
            }

            using (Brush brush = new SolidBrush(textColor))
            {
                // 上面显示图标
                rect.Inflate(0, -15);
                int x = rect.X, y = rect.Y, w = rect.Width, h = rect.Height;

                Rectangle r1 = new Rectangle(x, y, w, 35);
                Rectangle r2 = new Rectangle(x, y + 35, w, 35);

                if (item.icon != null)
                {
                    Rectangle dstRect = AfGraphicUtil.CenterInside(r1, new Size(28, 28));
                    g.DrawImage(item.icon, dstRect);
                }

                StringFormat sf = new StringFormat();
                sf.Alignment = StringAlignment.Center;
                sf.LineAlignment = StringAlignment.Center;
                g.DrawString(text, this.Font, brush, r2, sf);
            }
        }
    }
}
