﻿using MyListBox;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace CrossLeading.Forms
{
    public partial class UACEditForm : Form
    {
        public bool isOK;
        public UACEditForm()
        {
            isOK = false;
            InitializeComponent();
            InitCbDev();
        }

        private void InitCbDev()
        {
            // 首先清除下拉列表内容
            cb_dev.Items.Clear(); 

            Form1.LoadDevConf();
            foreach (KeyValuePair<string, DevConf> kv in Form1.devConfDict)
            {
                string devName = kv.Key;
                DevConf devConf = kv.Value;
                if (devName.StartsWith("QD") && devConf.isQD)
                {
                    cb_dev.Items.Add(devName);
                }
            }

            if(cb_dev.Items.Count == 0)
            {
                cb_dev.Items.Add("无多波束设备");
            }
            cb_dev.Text = cb_dev.Items[0].ToString();
        }

        private void btn_OK_Click(object sender, EventArgs e)
        {
            isOK = true;
            this.Close();
        }

        private void btn_Cancel_Click(object sender, EventArgs e)
        {
            isOK = false;
            this.Close();
        }
    }
}
