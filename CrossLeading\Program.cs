﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using System.Runtime.InteropServices;
using MyListBox;
using CrossLeading.Test;

namespace CrossLeading
{
    static class Program
    {
        [DllImport("kernel32.dll")]
        private static extern bool AllocConsole();

        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main(string[] args)
        {
            // 检查是否有命令行参数运行测试
            if (args.Length > 0 && args[0].ToLower() == "test")
            {
                // 分配控制台窗口
                AllocConsole();

                Console.WriteLine("=== 坐标变换算法测试程序 ===");
                Console.WriteLine("启动时间: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                Console.WriteLine();

                try
                {
                    // 运行坐标变换测试
                    CoordinateTransformTest.RunTest();
                }
                catch (Exception ex)
                {
                    Console.WriteLine("程序执行出错: " + ex.Message);
                    Console.WriteLine("详细信息: " + ex.StackTrace);
                }

                Console.WriteLine("\n程序执行完毕，按任意键退出...");
                Console.ReadKey();
                return;
            }

            if (IsSingleProcess())
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                try
                {
                    Application.Run(new Form1());
                }
                catch (Exception e)
                {
                    MessageBox.Show("系统找不到Config配置文件！");
                }
            }
            else
            {
                MessageBox.Show("应用程序已启动！");
            }

        }
        private static bool IsSingleProcess()
        {
            //主程序唯一
            int i = System.Diagnostics.Process.GetProcessesByName(
                            System.Diagnostics.Process.GetCurrentProcess().ProcessName).Length;
            if (i == 1)
                return true;
            else
                return false;

        }
    }
}
