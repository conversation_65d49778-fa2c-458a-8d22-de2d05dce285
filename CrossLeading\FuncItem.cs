﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MyListBox
{
    class FuncItem
    {
        public string text;
        public string cmd;
        public Image icon;

        public FuncItem()
        {

        }

        public FuncItem(string text, string cmd, Image icon)
        {
            this.text = text;
            this.cmd = cmd;
            this.icon = icon;
        }

        public override string ToString()
        {
            return text;

        }
    }
}
