# W6E数据模拟器使用说明

## 概述
这个Python脚本用于模拟W6E测角数据，通过UDP组播方式发送数据。数据格式完全参考`LeadingProcess.cs`中的`FormtW6E`函数和`W6EData`结构体。

## 功能特点
- 模拟真实的W6E测角数据包
- 支持UDP组播发送
- 连续变化的方位角(AZ)和俯仰角(EL)数据
- 真实的时间戳（积秒格式）
- 可配置的发送间隔和模拟时长

## 数据格式说明

### EMBL包头格式（24字节）
```
字节偏移  | 字段名  | 类型   | 说明
---------|--------|--------|------------------
0-3      | Time   | uint32 | 积秒（0.1毫秒单位）
4-7      | MID    | uint32 | 任务标识
8-11     | BID    | uint32 | 信息类别(0x00100605)
12-15    | Res1   | uint32 | 保留字段1
16-19    | Res2   | uint32 | 保留字段2
20-23    | LEN    | uint32 | 数据字段长度(23)
```

### W6E数据格式（23字节）
```
字节偏移  | 字段名     | 类型   | 说明
---------|-----------|--------|------------------
0        | 状态字节   | byte   | 0x02=自跟踪, 0x00=非自跟踪
1-4      | Time      | uint32 | 时间戳（积秒）
5-8      | AZ_Raw    | uint32 | 方位角原始值
9-12     | EL_Raw    | uint32 | 俯仰角原始值
13-22    | 填充      | bytes  | 填充字节
```

### 角度转换公式
```
度数 = 原始值 / (2^32) * 360
原始值 = 度数 * (2^32) / 360
```

## 使用方法

### 基本使用
```bash
# 基础版本
python w6e_simulator.py

# 增强版本
python w6e_simulator_advanced.py
```

### 增强版本功能

#### 配置文件支持
```bash
# 使用配置文件
python w6e_simulator_advanced.py -c w6e_config.json

# 显示当前配置
python w6e_simulator_advanced.py --show-config
```

#### 命令行参数
```bash
# 自定义网络参数
python w6e_simulator_advanced.py --multicast-ip *************** --port 9090

# 自定义时间参数
python w6e_simulator_advanced.py --duration 60 --interval 10

# 组合使用
python w6e_simulator_advanced.py -c w6e_config.json --duration 120
```

### 基础版本自定义参数
```python
# 修改组播地址和端口
simulator = W6ESimulator("***************", 8080)

# 设置角度参数
simulator.az_start = 45.0   # 起始方位角（度）
simulator.el_start = 30.0   # 起始俯仰角（度）
simulator.az_rate = 2.0     # 方位角变化率（度/秒）
simulator.el_rate = 1.0     # 俯仰角变化率（度/秒）

# 开始发送：30秒时长，每20秒发送一次
simulator.send_data(duration_seconds=30, interval_seconds=20)
```

### 配置文件格式
配置文件使用JSON格式，支持以下参数：
```json
{
  "network": {
    "multicast_ip": "***************",
    "port": 8080
  },
  "simulation": {
    "duration_seconds": 30,
    "interval_seconds": 20
  },
  "angles": {
    "az_start": 45.0,
    "el_start": 30.0,
    "az_rate": 2.0,
    "el_rate": 1.0
  },
  "advanced": {
    "sine_amplitude_az": 2.0,
    "sine_frequency_az": 0.1,
    "cosine_amplitude_el": 1.0,
    "cosine_frequency_el": 0.15
  }
}
```

## 模拟数据特点

### 角度变化模式
- **方位角**: 线性变化 + 小幅正弦波动，模拟真实跟踪运动
- **俯仰角**: 线性变化 + 小幅余弦波动，限制在0-90度范围内

### 时间连续性
- 使用系统当前时间计算积秒值
- 确保时间戳的连续性和准确性

### 数据真实性
- 状态字节设置为0x02（自跟踪模式）
- 角度转换严格按照C#代码中的公式
- 包头格式完全符合EMBL标准

## 输出示例
```
W6E数据模拟器
==================================================
UDP组播socket已设置: ***************:8080
开始发送W6E数据模拟...
组播地址: ***************:8080
发送间隔: 20秒
模拟时长: 30秒
------------------------------------------------------------
时间: 14:30:15.123, 方位: 45.123°, 俯仰: 30.456°, 积秒: 522151230
时间: 14:30:35.124, 方位: 85.234°, 俯仰: 50.567°, 积秒: 522351240
模拟完成
```

## 网络配置

### 组播地址选择
- 默认使用 `***************:8080`
- 可根据实际网络环境调整
- 确保接收端监听相同的组播地址和端口

### 防火墙设置
- 确保UDP端口未被防火墙阻止
- 允许组播流量通过

## 快速测试

### 一键测试脚本
```bash
# Windows用户
run_w6e_test.bat

# 手动测试
# 终端1：启动模拟器
python w6e_simulator.py

# 终端2：启动接收器
python test_w6e_receiver.py
```

### 测试验证
运行测试后，您应该看到：
1. **模拟器输出**: 显示发送的角度和时间数据
2. **接收器输出**: 显示接收到的数据包解析结果
3. **数据一致性**: 接收器解析的角度应与模拟器发送的角度一致

## 依赖要求
- Python 3.6+
- 标准库：socket, struct, time, math, datetime, threading, json, argparse

## 注意事项
1. 确保网络支持组播
2. 接收端需要正确解析EMBL包头和W6E数据
3. 时间戳使用当日积秒，跨日需要特殊处理
4. 角度值会自动处理负值和超过360度的情况

## 故障排除

### 常见问题
1. **无法发送数据**: 检查网络权限和防火墙设置
2. **接收端收不到数据**: 确认组播地址和端口配置
3. **角度数据异常**: 检查转换公式和数据类型

### 调试模式
脚本会实时输出发送的数据信息，包括时间戳、角度值等，便于调试和验证。
