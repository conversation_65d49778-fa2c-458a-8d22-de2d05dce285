using System;
using System.Collections.Generic;
using System.IO;
using System.Diagnostics;
using System.Xml;
using System.Globalization;
using CrossLeading;
using CrossLeading.Models;
using CrossLeading.Cards;

namespace CoordinateTransformTest
{
    /// <summary>
    /// 坐标变换算法测试程序
    /// </summary>
    class Program
    {
        // 测试参数
        private static readonly double ERROR_THRESHOLD = 1.0; // 误差阈值(米)
        private static readonly double TIME_SPAN_MINUTES = 30.0; // 测试时间跨度(分钟)
        private static readonly double TIME_STEP_SECONDS = 60.0; // 时间步长(秒)
        
        // 测试站点配置(使用默认测站)
        private static readonly DevConf TEST_STATION = new DevConf
        {
            devName = "TEST-STATION",
            lati = 46.747872,  // 纬度(度)
            longi = 130.290212, // 经度(度)
            height = 128.811   // 高程(米)
        };

        static void Main(string[] args)
        {
            Console.WriteLine("=== 坐标变换算法测试程序 ===");
            Console.WriteLine($"误差阈值: {ERROR_THRESHOLD} 米");
            Console.WriteLine($"测试时间跨度: {TIME_SPAN_MINUTES} 分钟");
            Console.WriteLine($"时间步长: {TIME_STEP_SECONDS} 秒");
            Console.WriteLine($"测试站点: {TEST_STATION.devName} ({TEST_STATION.lati}°, {TEST_STATION.longi}°, {TEST_STATION.height}m)");
            Console.WriteLine();

            try
            {
                // 1. 读取卫星轨道根数
                List<SatelliteOrbitElement> satellites = ReadSatelliteOrbits();
                Console.WriteLine($"读取到 {satellites.Count} 个卫星轨道根数");

                int passCount = 0;
                int totalCount = satellites.Count;

                // 2. 逐个测试每个卫星
                foreach (var satellite in satellites)
                {
                    Console.WriteLine($"\n--- 测试卫星: {satellite.Name} ---");
                    
                    if (TestSatelliteCoordinateTransform(satellite))
                    {
                        Console.WriteLine($"✓ {satellite.Name} 坐标变换测试通过");
                        passCount++;
                    }
                    else
                    {
                        Console.WriteLine($"✗ {satellite.Name} 坐标变换测试失败");
                    }
                }

                // 3. 输出测试结果
                Console.WriteLine("\n=== 测试结果汇总 ===");
                Console.WriteLine($"总测试数量: {totalCount}");
                Console.WriteLine($"通过数量: {passCount}");
                Console.WriteLine($"失败数量: {totalCount - passCount}");
                Console.WriteLine($"通过率: {(double)passCount / totalCount * 100:F1}%");

                if (passCount == totalCount)
                {
                    Console.WriteLine("🎉 所有测试通过！坐标变换算法正确。");
                }
                else
                {
                    Console.WriteLine("⚠️ 部分测试失败，请检查坐标变换算法。");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex.StackTrace}");
            }

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 读取卫星轨道根数
        /// </summary>
        private static List<SatelliteOrbitElement> ReadSatelliteOrbits()
        {
            string xmlPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SatOrb.xml");
            if (!File.Exists(xmlPath))
            {
                throw new FileNotFoundException($"卫星轨道文件不存在: {xmlPath}");
            }

            return SatelliteOrbitManager.LoadFromXml(xmlPath);
        }

        /// <summary>
        /// 测试单个卫星的坐标变换
        /// </summary>
        private static bool TestSatelliteCoordinateTransform(SatelliteOrbitElement satellite)
        {
            try
            {
                // 1. 写入轨道根数并计算轨道
                List<SatelliteOrbitData> orbitData = ComputeOrbitData(satellite);
                if (orbitData.Count == 0)
                {
                    Console.WriteLine("  轨道计算失败，无有效数据");
                    return false;
                }

                Console.WriteLine($"  计算得到 {orbitData.Count} 个轨道点");

                // 2. 执行坐标变换测试
                return PerformCoordinateTransformTest(orbitData);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  测试异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 计算轨道数据
        /// </summary>
        private static List<SatelliteOrbitData> ComputeOrbitData(SatelliteOrbitElement satellite)
        {
            string workingDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "02轨道外推");
            string orbInFile = Path.Combine(workingDir, "orb_eph_orbit.ifm");
            string orbOutFile = Path.Combine(workingDir, "orb_eph_start.ifm");
            string ephFile = Path.Combine(workingDir, "eph_ecf.txt");
            string orbExe = Path.Combine(workingDir, "orb_eph.exe");

            // 确保目录存在
            Directory.CreateDirectory(workingDir);

            // 1. 写入轨道根数
            string orbData = satellite.ToOrbDataString();
            File.WriteAllText(orbInFile, orbData);

            // 2. 写入计算参数
            DateTime startTime = DateTime.Now;
            WriteOrbitStartFile(orbOutFile, startTime, TIME_SPAN_MINUTES, TIME_STEP_SECONDS);

            // 3. 调用轨道外推程序
            if (!File.Exists(orbExe))
            {
                throw new FileNotFoundException($"轨道外推程序不存在: {orbExe}");
            }

            ProcessStartInfo startInfo = new ProcessStartInfo
            {
                FileName = orbExe,
                WorkingDirectory = workingDir,
                UseShellExecute = false,
                CreateNoWindow = true,
                RedirectStandardOutput = true,
                RedirectStandardError = true
            };

            using (Process process = Process.Start(startInfo))
            {
                process.WaitForExit(30000); // 30秒超时

                if (!process.HasExited)
                {
                    process.Kill();
                    throw new TimeoutException("轨道外推计算超时");
                }

                if (process.ExitCode != 0 && process.ExitCode != 1)
                {
                    string error = process.StandardError.ReadToEnd();
                    throw new Exception($"轨道外推失败，退出代码: {process.ExitCode}, 错误: {error}");
                }
            }

            // 4. 读取结果
            if (!File.Exists(ephFile))
            {
                throw new FileNotFoundException($"轨道数据文件不存在: {ephFile}");
            }

            return ReadOrbitData(ephFile);
        }

        /// <summary>
        /// 写入轨道计算开始文件
        /// </summary>
        private static void WriteOrbitStartFile(string filePath, DateTime startTime, double spanMinutes, double timeStep)
        {
            DateTime endTime = startTime.AddMinutes(spanMinutes);
            
            string content = $"{startTime.Year} {startTime.Month} {startTime.Day} " +
                           $"{startTime.Hour} {startTime.Minute} {startTime.Second}\n" +
                           $"{endTime.Year} {endTime.Month} {endTime.Day} " +
                           $"{endTime.Hour} {endTime.Minute} {endTime.Second}\n" +
                           $"{timeStep}";
            
            File.WriteAllText(filePath, content);
        }

        /// <summary>
        /// 读取轨道数据
        /// </summary>
        private static List<SatelliteOrbitData> ReadOrbitData(string filePath)
        {
            var orbitData = new List<SatelliteOrbitData>();
            string[] lines = File.ReadAllLines(filePath);

            foreach (string line in lines)
            {
                if (string.IsNullOrWhiteSpace(line)) continue;

                string[] parts = line.Trim().Split(new char[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length >= 12)
                {
                    try
                    {
                        var data = new SatelliteOrbitData
                        {
                            Time = new DateTime(
                                int.Parse(parts[0]),
                                int.Parse(parts[1]),
                                int.Parse(parts[2]),
                                int.Parse(parts[3]),
                                int.Parse(parts[4]),
                                (int)double.Parse(parts[5])
                            ),
                            X = double.Parse(parts[6]),
                            Y = double.Parse(parts[7]),
                            Z = double.Parse(parts[8]),
                            Vx = double.Parse(parts[9]),
                            Vy = double.Parse(parts[10]),
                            Vz = double.Parse(parts[11])
                        };
                        orbitData.Add(data);
                    }
                    catch
                    {
                        // 跳过解析失败的行
                    }
                }
            }

            return orbitData;
        }

        /// <summary>
        /// 执行坐标变换测试
        /// </summary>
        private static bool PerformCoordinateTransformTest(List<SatelliteOrbitData> orbitData)
        {
            int errorCount = 0;
            double maxError = 0.0;
            double totalError = 0.0;

            foreach (var orbit in orbitData)
            {
                // 原始XYZ坐标
                double[] originalXYZ = { orbit.X, orbit.Y, orbit.Z, orbit.Vx, orbit.Vy, orbit.Vz };

                // 步骤2: XYZ → AER (仿照VisibilityPredictCard.cs)
                double[] aer = ConvertXYZToAER(originalXYZ, TEST_STATION);

                // 步骤3: AER → XYZ (仿照LeadingProcess.cs)
                double[] reconstructedXYZ = ConvertAERToXYZ(aer, TEST_STATION);

                // 计算误差
                double error = CalculatePositionError(originalXYZ, reconstructedXYZ);
                totalError += error;

                if (error > maxError)
                {
                    maxError = error;
                }

                if (error > ERROR_THRESHOLD)
                {
                    errorCount++;
                    Console.WriteLine($"    时间: {orbit.Time:yyyy-MM-dd HH:mm:ss}, 误差: {error:F3}m (超出阈值)");
                }
            }

            double avgError = totalError / orbitData.Count;
            Console.WriteLine($"  平均误差: {avgError:F3}m, 最大误差: {maxError:F3}m, 超限点数: {errorCount}/{orbitData.Count}");

            return errorCount == 0;
        }

        /// <summary>
        /// XYZ转AER (仿照VisibilityPredictCard.cs)
        /// </summary>
        private static double[] ConvertXYZToAER(double[] satXYZ, DevConf station)
        {
            // 测站坐标转换
            double stationLon = station.longi * SatelliteMathUtils.DEG_TO_RAD;
            double stationLat = station.lati * SatelliteMathUtils.DEG_TO_RAD;
            double stationHeight = station.height;

            var (stationECEF, transformMatrix) = SatelliteMathUtils.ComputeStationCoordinate(
                stationLon, stationLat, stationHeight);

            // 计算AER
            double[] satECEF = { satXYZ[0], satXYZ[1], satXYZ[2] };
            var (azimuth, elevation, range) = SatelliteMathUtils.ComputeSatAccessAER(
                satECEF, stationECEF, transformMatrix);

            // 转换为度并返回
            return new double[] 
            { 
                range, 
                azimuth * SatelliteMathUtils.RAD_TO_DEG, 
                elevation * SatelliteMathUtils.RAD_TO_DEG 
            };
        }

        /// <summary>
        /// AER转XYZ (仿照LeadingProcess.cs)
        /// </summary>
        private static double[] ConvertAERToXYZ(double[] aer, DevConf station)
        {
            // 构造RAE数组 (仿照LeadingProcess.cs)
            double[] RAE = new double[6];
            RAE[0] = aer[0]; // 距离(米)
            RAE[1] = aer[1]; // 方位角(度)
            RAE[2] = aer[2]; // 俯仰角(度)

            // 测站BLH
            double[] SourceBLH = { station.lati, station.longi, station.height };

            // 坐标变换: RAE → staXYZ → Gxyz
            double[] staXYZ = OrbitForcast.RAE2staRxyz(RAE);
            double[] Gxyz = OrbitForcast.staRxyz2Gxyz(staXYZ, SourceBLH);

            return Gxyz;
        }

        /// <summary>
        /// 计算位置误差
        /// </summary>
        private static double CalculatePositionError(double[] xyz1, double[] xyz2)
        {
            double dx = xyz1[0] - xyz2[0];
            double dy = xyz1[1] - xyz2[1];
            double dz = xyz1[2] - xyz2[2];

            return Math.Sqrt(dx * dx + dy * dy + dz * dz);
        }
    }
}